<template>

    <!-- 第一层：学校 -->
<div v-for="(school, schoolIndex) in schools" :key="schoolIndex">
  <!-- 第二层：年级 -->
  <div v-for="(grade, gradeIndex) in school.grades" :key="gradeIndex">
    <!-- 第三层：学生 -->
    <div v-for="(student, studentIndex) in grade.students" :key="studentIndex">
    </div>
  </div>
</div>
</template>

<script setup>
let schools = [
  {
    name: "学校名",
    grades: [
      {
        name: "年级名", 
        students: [
          { id: "学号", name: "姓名", age: 年龄 }
        ]
      }
    ]
  }
]
</script>