<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue3 三维数组循环示例</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        .container {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .school {
            border: 2px solid #007bff;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .grade {
            border: 1px solid #28a745;
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #e9f7ef;
        }
        .student {
            display: inline-block;
            margin: 5px;
            padding: 8px 12px;
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .school-title {
            color: #007bff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .grade-title {
            color: #28a745;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .student-name {
            color: #495057;
            font-weight: 500;
        }
        .student-age {
            color: #6c757d;
            font-size: 12px;
        }
        .stats {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .add-form {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>Vue3 三维数组循环示例 - 学校管理系统</h1>
            
            <!-- 统计信息 -->
            <div class="stats">
                <h3>统计信息</h3>
                <p>总学校数: {{ schools.length }}</p>
                <p>总年级数: {{ totalGrades }}</p>
                <p>总学生数: {{ totalStudents }}</p>
            </div>

            <!-- 添加学生表单 -->
            <div class="add-form">
                <h3>添加新学生</h3>
                <div class="form-group">
                    <label>学校:</label>
                    <select v-model="newStudent.schoolIndex">
                        <option v-for="(school, index) in schools" :key="index" :value="index">
                            {{ school.name }}
                        </option>
                    </select>
                </div>
                <div class="form-group">
                    <label>年级:</label>
                    <select v-model="newStudent.gradeIndex" :disabled="newStudent.schoolIndex === null">
                        <option v-for="(grade, index) in getGradesForSchool(newStudent.schoolIndex)" :key="index" :value="index">
                            {{ grade.name }}
                        </option>
                    </select>
                </div>
                <div class="form-group">
                    <label>姓名:</label>
                    <input v-model="newStudent.name" type="text" placeholder="请输入学生姓名">
                </div>
                <div class="form-group">
                    <label>年龄:</label>
                    <input v-model="newStudent.age" type="number" placeholder="请输入年龄">
                </div>
                <button class="btn btn-primary" @click="addStudent">添加学生</button>
            </div>

            <!-- 三维数组循环显示 -->
            <div v-for="(school, schoolIndex) in schools" :key="schoolIndex" class="school">
                <div class="school-title">
                    🏫 {{ school.name }} ({{ school.location }})
                    <button class="btn btn-danger" @click="removeSchool(schoolIndex)">删除学校</button>
                </div>
                
                <div v-for="(grade, gradeIndex) in school.grades" :key="gradeIndex" class="grade">
                    <div class="grade-title">
                        📚 {{ grade.name }} - 共{{ grade.students.length }}名学生
                        <button class="btn btn-danger" @click="removeGrade(schoolIndex, gradeIndex)">删除年级</button>
                    </div>
                    
                    <div v-if="grade.students.length === 0" style="color: #6c757d; font-style: italic;">
                        暂无学生
                    </div>
                    
                    <div v-for="(student, studentIndex) in grade.students" :key="studentIndex" class="student">
                        <div class="student-name">👤 {{ student.name }}</div>
                        <div class="student-age">年龄: {{ student.age }}岁</div>
                        <div class="student-age">学号: {{ student.id }}</div>
                        <button class="btn btn-danger" style="font-size: 10px; padding: 2px 6px;" 
                                @click="removeStudent(schoolIndex, gradeIndex, studentIndex)">删除</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                // 三维数组数据结构: 学校 -> 年级 -> 学生
                const schools = ref([
                    {
                        name: "北京第一中学",
                        location: "北京市朝阳区",
                        grades: [
                            {
                                name: "高一年级",
                                students: [
                                    { id: "2024001", name: "张三", age: 16 },
                                    { id: "2024002", name: "李四", age: 15 },
                                    { id: "2024003", name: "王五", age: 16 }
                                ]
                            },
                            {
                                name: "高二年级", 
                                students: [
                                    { id: "2023001", name: "赵六", age: 17 },
                                    { id: "2023002", name: "钱七", age: 16 }
                                ]
                            },
                            {
                                name: "高三年级",
                                students: [
                                    { id: "2022001", name: "孙八", age: 18 },
                                    { id: "2022002", name: "周九", age: 17 },
                                    { id: "2022003", name: "吴十", age: 18 }
                                ]
                            }
                        ]
                    },
                    {
                        name: "上海实验中学",
                        location: "上海市浦东新区",
                        grades: [
                            {
                                name: "初一年级",
                                students: [
                                    { id: "2024101", name: "陈一", age: 13 },
                                    { id: "2024102", name: "林二", age: 12 }
                                ]
                            },
                            {
                                name: "初二年级",
                                students: [
                                    { id: "2023101", name: "黄三", age: 14 },
                                    { id: "2023102", name: "郑四", age: 13 },
                                    { id: "2023103", name: "刘五", age: 14 }
                                ]
                            }
                        ]
                    },
                    {
                        name: "广州外国语学校",
                        location: "广州市天河区", 
                        grades: [
                            {
                                name: "小学六年级",
                                students: [
                                    { id: "2024201", name: "何六", age: 12 },
                                    { id: "2024202", name: "邓七", age: 11 }
                                ]
                            }
                        ]
                    }
                ]);

                // 新学生表单数据
                const newStudent = ref({
                    schoolIndex: null,
                    gradeIndex: null,
                    name: '',
                    age: ''
                });

                // 计算属性：总年级数
                const totalGrades = computed(() => {
                    return schools.value.reduce((total, school) => total + school.grades.length, 0);
                });

                // 计算属性：总学生数
                const totalStudents = computed(() => {
                    return schools.value.reduce((total, school) => {
                        return total + school.grades.reduce((gradeTotal, grade) => {
                            return gradeTotal + grade.students.length;
                        }, 0);
                    }, 0);
                });

                // 获取指定学校的年级列表
                const getGradesForSchool = (schoolIndex) => {
                    if (schoolIndex === null || schoolIndex === undefined) return [];
                    return schools.value[schoolIndex]?.grades || [];
                };

                // 添加学生
                const addStudent = () => {
                    if (newStudent.value.schoolIndex === null || 
                        newStudent.value.gradeIndex === null || 
                        !newStudent.value.name || 
                        !newStudent.value.age) {
                        alert('请填写完整信息');
                        return;
                    }

                    const studentId = Date.now().toString();
                    const student = {
                        id: studentId,
                        name: newStudent.value.name,
                        age: parseInt(newStudent.value.age)
                    };

                    schools.value[newStudent.value.schoolIndex].grades[newStudent.value.gradeIndex].students.push(student);
                    
                    // 重置表单
                    newStudent.value = {
                        schoolIndex: null,
                        gradeIndex: null,
                        name: '',
                        age: ''
                    };
                };

                // 删除学生
                const removeStudent = (schoolIndex, gradeIndex, studentIndex) => {
                    if (confirm('确定要删除这名学生吗？')) {
                        schools.value[schoolIndex].grades[gradeIndex].students.splice(studentIndex, 1);
                    }
                };

                // 删除年级
                const removeGrade = (schoolIndex, gradeIndex) => {
                    if (confirm('确定要删除这个年级吗？这将删除该年级下的所有学生！')) {
                        schools.value[schoolIndex].grades.splice(gradeIndex, 1);
                    }
                };

                // 删除学校
                const removeSchool = (schoolIndex) => {
                    if (confirm('确定要删除这所学校吗？这将删除该学校下的所有年级和学生！')) {
                        schools.value.splice(schoolIndex, 1);
                    }
                };

                return {
                    schools,
                    newStudent,
                    totalGrades,
                    totalStudents,
                    getGradesForSchool,
                    addStudent,
                    removeStudent,
                    removeGrade,
                    removeSchool
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
