<!--积分详情--审计人员维度->-->
<!--#include virtual ="include/header.html"-->
<link
        href="resource/css/audit/online/tagmodel/tagIndex.css?v=6.5"
        rel="stylesheet"
        type="text/css"
/>
<link
        href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5"
        rel="stylesheet"
        type="text/css"
/>
<style>
    .layui-table {
        margin: 0
    }

    .layui-table tr td {
        padding: 2px 0px;
    }

    /*.layui-table tr td .layui-table-cell {*/
    /*    height: 100px;*/
    /*    line-height: 20px;*/
    /*    display: flex;*/
    /*    align-items: center;*/
    /*    overflow: hidden;*/
    /*    text-overflow: inherit;*/
    /*    white-space: initial;*/
    /*    box-sizing: border-box;*/
    /*    font-size: 14px;*/
    /*    font-family: PingFangSC-Regular, PingFang SC;*/
    /*    font-weight: 400;*/
    /*    color: #333333;*/
    /*}*/

    .layui-laydate .laydate-btns-clear {
        display: inline-block;
    }

    .layui-common-body {
        background: #fff;
        padding: 10px 20px;
    }

    .last-btn .last-btn-one:nth-child(4) {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #2999ff;
        background-color: #e6f7ff;
        margin-left: 10px;
    }

    .form-search {
        width: 100%;
        height: 70px;
        background: #f4f4f4;
        opacity: 1;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        padding: 0px 15px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        align-items: center;
    }

    .left-name {
        width: 100px;
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #333333;
        min-width: 56px;
        text-align: right;
        padding-right: 6px;
    }

    .left-search {
        flex: 1;
    }

    .left-search .width-20 {
        display: flex;
        align-items: center;
    }

    .layui-input.layui-unselect,
    input {
        height: 32px;
        background: rgba(255, 255, 255, 1);
        border: 1px solid #d9d9d9;
        border-radius: 1px;
        box-sizing: border-box;
        padding-left: 5px;
    }

    .last-btn {
        text-align: right;
        justify-content: end;
    }

    .last-btn .model-btn {
        margin-left: 4px;
    }

    .last-btn .last-btn-one {
        height: 32px;
        opacity: 1;
        border-radius: 4px;
        box-sizing: border-box;
        padding: 0px 15px;
        display: flex;
        align-items: center;
        cursor: pointer;
    }


    .tables {
        margin-top: 10px;
    }

    .icon-bianji {
        color: #f5222d;
        font-size: 18px;
        margin-right: 15px;
        cursor: pointer;
    }

    .icon-chakan1 {
        color: #f5222d;
        font-size: 17px;
        cursor: pointer;
    }

    .integral-list {
        float: right;
        margin-left: 20px;
    }
    .layui-table-body {
        overflow-x: hidden;
    }
    .integral-li {
        margin-right: 20px;
        float: left;
    }
    .integral-li-one{
        font-weight: bold;
        margin-right:30px;
    }
    .layui-input.layui-unselect, input {
        width: 100%;
    }

    .right-value {
        width: calc(100% - 100px);
    }

    .integral-lable {
        margin-right: 6px;
    }

    .integral-num {
        color: #f5222d;
    }

    .model-btn {
        margin: 2px 0;
    }

    .layui-table-cell {
        height: auto;
    }

    .text-center {
        width: 100%;
        text-align: center;
    }

    td[data-field='name'] > div div {
        padding: 0px 10px;
        height: 100px;
        line-height: 20px;
        text-align: left;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
        cursor: pointer;
        color: #c00;
        text-decoration: underline;
    }

    td[data-field='name'] > div div a {
        color: #c00;
    }

    .layui-table tr td[data-field='numbers'] .layui-table-cell,
    .layui-table tr td[data-field='integralType'] .layui-table-cell,
    .layui-table tr td[data-field='number'] .layui-table-cell,
    .layui-table tr td[data-field='sourceDate'] .layui-table-cell,
    .layui-table tr td[data-field='integral'] .layui-table-cell,
    .layui-table tr td[data-field='untegralDate'] .layui-table-cell,
    .layui-table tr td[data-field='teamName'] .layui-table-cell {
        justify-content: center;
    }
    .layui-layer-content-my{
        background: none;
        padding: 0;
        box-shadow: 0 1px 6px rgba(0,0,0,.1);
        position: relative;
        line-height: 22px;
        min-width: 12px;
        font-size: 12px;
        _float: left;
        border-radius: 4px;
        color: #fff;
        position: absolute;
    }
    .layui-table-tips-main-my{
        max-height: 150px;
        padding: 8px 15px;
        padding-right: 18px;
        font-size: 14px;
        overflow-y: auto;
        background-color: #fff;
        color: #333;
        border-color: #dcdcdc;
        border-width: 1px;
        border-style: solid;
        margin-top: -10px;
    }
    .layui-table-tips-main-my .icon-guanbi1{
        position: absolute;
        right: 10px;
        top: -8px;
        font-size: 24px;
        cursor: pointer
    }
    .width-20{
        width: 20%;
        float:left;
    }
</style>

<body>
<div class="layui-row">
    <div class="layui-row layui-col-md12 layui-col-sm12 layui-col-lg12">
        <div class="layui-col-md12 layui-col-sm12 layui-col-lg12">
            <div class="layui-common-card parent-commom-card">
                <div class="layui-common-header">
                    <div class="layui-common-card-header">
                        <div class="layui-card-header-title">积分来源</div>
                    </div>
                </div>
                <div class="layui-common-body">
                    <div class="form-search">
                        <div class="left-search">
                            <div class="layui-row">
                                <div class="width-20">
                                    <div class="left-name">省分</div>
                                    <div class="right-value">
                                        <div class="layui-form">
                                            <select id = "provinceCode"></select>
                                        </div>
                                    </div>
                                </div>

                                <div class="width-20">
                                    <div class="left-name">所属团队</div>
                                    <div class="right-value">
                                        <div class="layui-form">
                                            <input id="groupName" placeholder="请输入"/>
                                        </div>
                                    </div>
                                </div>

                                <div class="width-20">
                                    <div class="left-name">积分账期</div>
                                    <div class="right-value">
                                        <div class="layui-form">
                                            <input id="untegralDate" placeholder="请选择" readonly/>
                                        </div>
                                    </div>
                                </div>
                                <div class="width-20">
                                    <div class="left-name">类型</div>
                                    <div class="right-value">
                                        <div class="layui-form">
                                            <select
                                                    id="objectType"
                                                    lay-filter="objectType"
                                                    lay-verify="objectType"
                                                    name="objectType"
                                            ></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="width-20 last-btn">
                                    <div class="model-btn model-btn-submit" onclick="tableLoad1()" title="查询">
                                        <i class="iconfont search-icon">&#xe60b;</i>
                                        <span>查询</span>
                                    </div>
                                    <br>
                                    <div class="model-btn model-btn-submit" onclick="resetBtn()" title="重置">
                                        <i class="iconfont search-icon">&#xe63a;</i>
                                        <span>重置</span>
                                    </div>
                                    <br>
                                    <div class="model-btn model-btn-reset" onclick="batchDownloadReportData()" title="导出">
                                        <i class="iconfont search-icon">&#xe75b;</i>
                                        <span>导出</span>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                    <div class="tables">

                        <table
                                class="layui-table jq-even"
                                id="table_1"
                                lay-filter="table_1"
                        ></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-menu">
        <a
                data-parent="true"
                href="javascript:"
                id="tableMenu"
                style="color: #c00; text-decoration: underline"
        >
            <i class="iconfont" data-icon=""></i>
        </a>
    </div>
</div>
<div class="layui-layer-content-my" style="display: none;">
    <div class="layui-table-tips-main-my">
        <div class="text-left" id="lastRisk"></div>
        <span class="icon iconfont icon-guanbi1" onclick="closeRisk()"></span>
    </div>
</div>
</body>

<!--#include virtual ="include/version.html"-->
<!--#include virtual ="include/tpl/select-cat.html"-->
<script src="resource/js/jquery/jquery.js" type="text/javascript"></script>

<script id="block4" type="text/html">
    <option value="">--请选择--</option>
    {{# layui.each(d, function(index, item){ }}
    <option value="{{item.code}}">{{item.codeText}}</option>
    {{# }); }}
</script>
<script>
    //风研主键
    if(getUrlParam("scoreStartMonth")){
        var startTime = getUrlParam("scoreStartMonth");
        var endTime = getUrlParam("scoreEndMonth");
         scoreStartMonth = startTime;
         scoreEndMonth = endTime;
        if(endTime != undefined && endTime != ''){
            $("#untegralDate").val(startTime + ' - '+endTime);
        }
    }

    layui.use(
        [
            "jqform",
            "table",
            "laytpl",
            "jquery",
            "laydate",
            "layer",
            "jqbind",
            "upload",
            "jqztree",
            "laypage",
            "jqfrm"
        ],
        function () {
            var $ = layui.jquery,

                frm = layui.jqfrm,
                ctx = top.global.ctx,
                $ZTree = layui.jqztree,
                layer = layui.layer,
                form = layui.jqform,
                laydate = layui.laydate,
                tpl = layui.laytpl,
                table = layui.table,
                upload = layui.upload,
                laypage = layui.laypage,
                jqbind = layui.jqbind;
            jqbind.init();
            //积分账期
            laydate.render({
                elem: '#untegralDate'
                , type: 'year'
                , range: true
                , done: function (value) {
                    if (value){
                        scoreStartMonth = value.split('-')[0].trim();
                        scoreEndMonth = value.split('-')[1].trim();
                    }else {
                        scoreStartMonth = '';
                        scoreEndMonth = '';
                    }
                }
            });
            //重置
            window.resetBtn = function () {
                $('#untegralDate').val('')
                $('#groupName').val('')
                $('#objectType').val('')
                $('#provinceCode').val('')
                scoreStartMonth=''
                scoreEndMonth=''
                paramsData = ''
                scoreType=''
                form.render()
                tableLoad1()
            }

            // // //积分来源
            // window.pointsSource = function () {
            //     $.ajax({
            //         url: ctx + "/bdata/remoteDataLeaderHome/queryStaticScoreInfo",
            //         dataType: "JSON",
            //         type: "POST",
            //         contentType: "application/json;charset=UTF-8",
            //         data:JSON.stringify({
            //             scoreStartMonth: scoreStartMonth,//积分账期
            //             scoreEndMonth: scoreEndMonth,//积分账期
            //             groupName: $("#groupName").val(),//团队名称
            //             provinceCode: $("#provinceCode").val(),//省分
            //             sourceEndMonth: sourceEndMonth,//来源产生账期
            //             researchIds:'',//风研主键
            //             objectNum: $("#objectNum").val(),//编号
            //             paramsData:'',
            //             scoreType:'' //查询类型  research  ：风研查询
            //         }),
            //         success: function (res) {
            //         },
            //         error: function (e) {
            //             console.info(e);
            //         },
            //     });
            // };
            //类型
            window.queryType = function () {
                $.ajax({
                    url: ctx + "/system/dict/queryDicListByType",
                    dataType: "JSON",
                    type: "POST",
                    contentType: "application/json",
                    data:JSON.stringify({
                        key:'score_source_type'
                    }),
                    success: function (res) {
                        lp(tpl, res.data, $("#block4").html(), $("#objectType"));
                        form.render();
                    },
                    error: function (e) {
                        console.info(e);
                    },
                });
            };
            queryType();
            var tableData = [];
            window.tableLoad1 = function (data) {
                console.info(data);
                table.render({
                    elem: "#table_1",
                    id: "table_1",
                    even: true,
                    url: ctx + "/bdata/remoteDataLeaderHome/showRiskPeopleScoreInfo",
                    where: {
                        objectNum: '',//名称
                        objectName: '',//通报名称/项目名称/问题标题
                        scoreStartMonth: scoreStartMonth,//积分账期
                        scoreEndMonth: scoreEndMonth,//积分账期
                        groupName: $("#groupName").val(),//团队名称
                        provinceCode: $("#provinceCode").val(),//省分
                        objectType: $("#objectType").val(),//类型
                        researchIds:'',//风研主键
                        paramsData:'',
                        scoreType:'', //查询类型  research  ：风研查询
                        status:1,
                        isDisplayed:1
                    },
                    merge: [[1],[2],[3]],
                    page: true,
                    height:getUrlParam("scoreStartMonth")?'full-200':'440',
                    cols: [
                        [
                            {field: "num", title: "序号", align: "center", width: "5%"},
                            {field: "userName", title: "姓名", align: "center", width: "6%"},
                            {field: "provName", title: "省分", align: "center", width: "5%"},
                            {field: "scoreSum", title: "积分合计", align: "center", width: "7%",
                                templet:function (d){
                                    return '<span data-code="'+d.userName+'">'+d.scoreSum+'</span>'
                                }
                            },
                            {field: "objectTypeName", title: "类型", align: "center", width: "8%"},
                            {field: "objectNum", title: "公文号/项目</br>编号/问题编号",style:'text-align:left', align: "center", width: "14%", },
                            {field: "objectName", title: "通报名称/项目名称/问题标题", align: "center", width: "20%",
                                templet: function (d) {
                                    var nameDiv = '<div class="text-left" title="'+d.objectName+'">';

                                    if (d.objectType == "1") {
                                        if(d.attachmentId){
                                            nameDiv += '<a style = "cursor:pointer;color: #c00;text-decoration:underline;" href="/jtauditwo/files/downLoad/'+encrypt(d.attachmentId)+'">'
                                                +d.objectName+'</a>'
                                        }else{
                                            nameDiv += d.objectName;
                                        }
                                    } else if (d.objectType == "2") {
                                        if(d.planProjectId){
                                            nameDiv +=
                                                '<div title="查看" style = "cursor:pointer;color: #c00;text-decoration:underline;" onclick=openModak("' + d.objectId + '","'+ d.planProjectId + '","' + d.projectTypeEnumId + '")>' +
                                                '<span>'+d.objectName+'</span></div>'
                                            ;
                                        }else{
                                        }
                                    }else if (d.objectType == "3") {
                                        nameDiv +=
                                            '<div title="查看" style = "cursor:pointer;color: #c00;text-decoration:underline;"  onclick=openDetails("' + d.objectId + '","'+ d.insId + '","' + d.proStatus + '")>' +
                                            '<span>'+d.objectName+'</span></div>'
                                        ;
                                    }
                                    return nameDiv + '</div>';
                                }
                            },
                            {field: "score", title: "积分", align: "center", width: "6%", },
                            {field: "createMonth", title: "积分账期", align: "center", width: "7%", },
                            {field: "sourceMonth", title: "来源产生账期", align: "center", width: "7%", },
                            {field: "groupName", title: "所属团队", align: "center", width: "8%"},
                            {field: "operate", title: "风研成果",  align: "center", width: "7%",
                                templet: function (d) {
                                    let text = "";
                                    text =
                                        '<div class="table-flex-j-s" style="text-align: center">'
                                    if (d.researchInfoId) {
                                        text +=  '<span class="icon iconfont text-red cursor table-btn" title="查看" onclick=detailLeaderManager("' + d.researchInfoId + '")>&#xe651;</span>'
                                    }
                                    text += '</div>';
                                    return text;

                                }
                            },
                        ]
                    ],
                    // height:'full-165',
                    done: function (res, curr, count) {
                        tableData = res.data
                    },
                });
            };
            //详情
            window.detailLeaderManager = function (id) {
                var indexs = top.layer.open({
                    title: '详情',
                    content:
                        "views/audit/bigdata/remoteDataKanban/riskResearchInput/wind-research-achievements-detail-new.html" +
                        "?id=" + id,
                    type: 2,
                    area: ["80%", "80%"],
                    fixed: true,
                    maxmin: false,
                    resize: false,
                    yes: function (index, layero) {

                    },
                    btn2: function (index, layero) {

                    },
                    success: function (layero, index) {
                    },
                });

            }
            window.closeRisk = function(){
                $('.layui-layer-content-my').hide()
            }

            window.openDetails = function(problemId,insId,proStatus){
                var height = $(window).height() * 1;
                var width = $(window).width() * 1;
                var flag = "1";
                if (proStatus == "0") {
                    flag = "2";
                }
                var index = top.layer.open({
                    type: 2, //类型
                    area: ["90%","90%"],
                    resize: false, //是否允许拉伸
                    title: "问题详情", //题目
                    shadeClose: true, //点击遮罩层关闭
                    content:
                        top.global.ctxStatic +
                        "/views/audit/pro/reform/reformreadrecord/reformTodoTaskBaseQuery.html" +
                        "?problemId=" + problemId +
                        "&flag=" + flag +
                        "&processInstanceId=" + insId,
                });
            }

            window.openModak = function(projectId,planProjectId,projectTypeEnumId) {
                var openUrl = "";
                if(projectTypeEnumId=='09'){
                    openUrl = "/views/audit/pro/projectmanagement/farprojectinformation/list.html?planProjectId="+planProjectId+"&projectId="+projectId;
                }else{
                    openUrl = "/views/audit/pro/projectmanagement/projectinformation/list.html?planProjectId="+planProjectId+"&projectId="+projectId;
                }
                layui.use(['layer','jqform','upload'], function() {
                    var frm = layui.jqform;
                    var layer = layui.layer,$ = layui.$;
                    var upload = layui.upload;
                    var iframeUrl = top.global.ctxStatic + openUrl;
                    var width = ($(window).width() * 1);
                    var height = ($(window).height() * 1);
                    var index = window.parent.layer.open({
                        type: 1,
                        area: ["95%","95%"],
                        resize: false,
                        title: '项目信息查看',
                        content: '<iframe name="fileiframe" id="fileiframe" src='+iframeUrl+' frameborder="0" align="left" width="100%" height="100%"></iframe>',
                        shadeClose: false
                    });
                });
            }

            window.involveProvinceList = function(){
                $.ajax({
                    url: ctx + "/bdata/remoteDataLeaderHome/queryAllProvList",
                    type: "POST",
                    dataType: "json",
                    success: function (ret) {
                        if (ret.httpCode == 200) {
                            var data = ret.data;
                            lp(tpl, data.list, $("#select-tpl-list").html(), $("#provinceCode"));
                            if(provinceCode){
                                $("#provinceCode").val(provinceCode);
                            }
                            form.render();
                            //调用加载页面
                            // pointsSource();
                            tableLoad1();
                        } else {
                            frm.error(ret.msg);
                        }
                    },
                    error: function (ret) {
                        frm.error("网络连接失败！");
                    }
                });

            }
            involveProvinceList();
            /**
             * 导出
             * @returns {boolean}
             */
            window.batchDownloadReportData = function () {
                var indexZG = layer.load(1, {//遮盖层
                    shade: [0.4, '#fff'] //0.4透明度的白色背景
                });
                $.ajax({
                    url: ctx + "/bdata/remoteDataLeaderHome/riskPeopleScoreSaveExportParameter",
                    type: "POST",
                    dataType: "JSON",
                    contentType: "application/json;charset=UTF-8",
                    data: JSON.stringify({
                        objectNum: '',//名称
                        objectName: '',//名称
                        scoreStartMonth: scoreStartMonth,//积分账期
                        scoreEndMonth: scoreEndMonth,//积分账期
                        groupName: $("#groupName").val(),//团队名称
                        provinceCode: $("#provinceCode").val(),//省分
                        objectType: $("#objectType").val(),//类型
                        researchIds:'',//风研主键
                        paramsData:'',
                        scoreType:'', //查询类型  research  ：风研查询
                        status:1,
                        isDisplayed:1
                    }),
                    success: function (key) {
                        window.location.href = ctx + "/bdata/remoteDataLeaderHome/exportRiskPeopleScoreInfo/" + key;
                        setTimeout(function(){
                            layer.close(indexZG);
                        },3000)
                    }
                });

            };
        }
    );
</script>
