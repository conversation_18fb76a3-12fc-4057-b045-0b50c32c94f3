<!--整体追责列表-->
<!--#include virtual ="include/header.html"-->

<body>
    <style>
        .layui-table-cell {
            height: auto;
            padding: 0 8px;
            line-height: 20px;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
        }

        /* 被检查单位选择器样式 */
        .checkedUnit-selector {
            position: relative;
            cursor: pointer;
        }

        .checkedUnit-selector .show_text {
            padding: 0px 12px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            background-color: #fff;
            color: #666;
            font-size: 14px;
            line-height: 30px;
            min-height: 30px;
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .checkedUnit-selector.havec .show_text {
            color: #333;
            border-color: #c20000;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .checkedUnit-selector .have_to_show {
            display: none;
        }

        .checkedUnit-selector .to_clean {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #999;
            display: none;
        }

        .checkedUnit-selector.havec .to_clean {
            display: block;
        }

        .checkedUnit-selector .moreToChoose {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-top: none;
            border-radius: 0 0 2px 2px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
            z-index: 999;
            display: none;
            max-height: 200px;
            overflow-y: auto;
        }

        .checkedUnit-selector:hover .moreToChoose,
        .checkedUnit-selector .moreToChoose:hover {
            display: block;
        }

        .into_choose {
            padding: 10px;
        }

        .into_choose .endStatus {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
            background-color: #fff;
        }

        .into_choose .endStatus:hover {
            border-color: #c20000;
            color: #c20000;
        }

        .into_choose .endStatus.haschoose {
            background-color: #c20000;
            color: #fff;
            border-color: #c20000;
        }

        /* A级专业选择器样式 */
        .aProfession-selector {
            position: relative;
            cursor: pointer;
        }

        .aProfession-selector .show_text {
            padding: 0px 12px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
            background-color: #fff;
            color: #666;
            font-size: 14px;
            line-height: 30px;
            min-height: 30px;
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .aProfession-selector.havec .show_text {
            color: #333;
            border-color: #c20000;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .aProfession-selector .have_to_show {
            display: none;
        }

        .aProfession-selector .to_clean {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #999;
            display: none;
        }

        .aProfession-selector.havec .to_clean {
            display: block;
        }

        .aProfession-selector .moreToChoose {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-top: none;
            border-radius: 0 0 2px 2px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
            z-index: 999;
            display: none;
            max-height: 200px;
            overflow-y: auto;
        }

        .aProfession-selector:hover .moreToChoose,
        .aProfession-selector .moreToChoose:hover {
            display: block;
        }

        /* 表格选择框样式优化 */
        .layui-table-view .layui-table td[data-field="LAY_CHECKED"] {
            padding: 0;
            text-align: center;
        }

        .layui-table-view .layui-table th[data-field="LAY_CHECKED"] {
            padding: 0;
            text-align: center;
        }

        /* 选中行高亮 */
        .layui-table-view .layui-table tbody tr.layui-table-click {
            background-color: #f2f2f2;
        }



    </style>
    <div class="layui-fluid larry-wrapper">
        <section class="panel panel-padding">
            <form class="layui-form layui-form-pane form-conmon form-conmon-more" data-params='{bind:true }'
                id="accountabilityForm">
                <!-- 第二行 -->
                <div class="layui-row layui-col-space10 transition-500ms search-condition" style="border: 0;">

                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">审结时间</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="auditCompleteTime" name="auditCompleteTime"
                                    placeholder="审结时间" readonly="readonly">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">被检查单位</label>
                            <div class="layui-input-block">
                                <div class="checkedUnit-selector">
                                    <div class="show_text">请选择</div>
                                    <div class="have_to_show" style="display: none;">被检查单位</div>
                                    <div class="to_clean" onclick="cleanItem('checkedUnit')" style="display: none;"><i
                                            class="layui-icon layui-icon-close"></i></div>
                                    <div class="moreToChoose">
                                        <div class="into_choose" data-type="only_true" id="checkedUnit"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">A级专业</label>
                            <div class="layui-input-block">
                                <div class="aProfession-selector">
                                    <div class="show_text">请选择</div>
                                    <div class="have_to_show" style="display: none;">A级专业</div>
                                    <div class="to_clean" onclick="cleanItem('aProfession')" style="display: none;"><i
                                            class="layui-icon layui-icon-close"></i></div>
                                    <div class="moreToChoose">
                                        <div class="into_choose" data-type="only_true" id="aProfession"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">项目ERP编号</label>
                            <div class="layui-input-block">
                                <input autocomplete="off" class="layui-input" id="projectErpCode" name="projectErpCode"
                                    placeholder="请输入项目ERP编号" type="text" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 第三行 -->
                <div class="layui-row layui-col-space10 transition-500ms search-condition"
                    style="margin-top: 10px; border: 0;">

                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">合同/订单编号</label>
                            <div class="layui-input-block">
                                <input autocomplete="off" class="layui-input" id="contractOrderCode"
                                    name="contractOrderCode" placeholder="请输入合同/订单编号" type="text" />
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">原审计机构名称</label>
                            <div class="layui-input-block">
                                <input autocomplete="off" class="layui-input" id="originalAuditOrgName"
                                    name="originalAuditOrgName" placeholder="请输入原审计机构名称" type="text" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">项目名称</label>
                            <div class="layui-input-block">
                                <input autocomplete="off" class="layui-input" id="projectName" name="projectName"
                                    placeholder="请输入项目名称" type="text" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">施工单位</label>
                            <div class="layui-input-block">
                                <input autocomplete="off" class="layui-input" id="constructionUnit"
                                    name="constructionUnit" placeholder="请输入施工单位" type="text" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 第四行 -->
                <div class="layui-row layui-col-space10 transition-500ms search-condition" style="margin-top: 10px;">


                    <div class="layui-col-md3 layui-col-sm3">
                        <div class="layui-form-item layui-form-item-sm">
                            <label class="layui-form-label">台账账期区间</label>
                            <div class="layui-input-block">
                                <input class="layui-input" id="ledgersMonth" name="ledgersMonth" placeholder="台账账期区间"
                                    readonly="readonly">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="search-btn" style="position:absolute;right:0px;top:0px;">
                    <div class="layui-form-item" style="display:inline-block;">
                        <a class="layui-btn layui-btn-sm" href="javascript:" onclick="reloadTable()">
                            <i class="iconfont search-icon">&#xe60b;</i> 查询</a>
                    </div>
                    <div class="layui-form-item" style="display:inline-block;">
                        <button class="layui-btn btn-outline layui-btn-normal layui-btn-sm btn" type="button" onclick="resetForm()">
                            <i class="iconfont search-icon">&#xe63a;</i> 重置
                        </button>
                    </div>
                </div>
            </form>
            <div class="layui-form" style="margin-top:10px">
                <div style="float: right">
                    <!-- style = "display: none;" -->
                    <a class="layui-btn btn-outline layui-btn-normal layui-btn-sm" id="fillBtn"
                        onclick="addExtractAudit()">
                        <i class="iconfont search-icon">&#xe635;</i> 添加抽查
                    </a>

                </div>

                <table class="layui-table jq-even" id="accountabilityTable" lay-filter="accountabilityTable"></table>
            </div>
        </section>
    </div>


</body>
<!--#include virtual ="include/version.html"-->
<!--列表查看-->
<script id="configBar" type="text/html">
    <a class="table-btn" type="button" title="查看" onclick="configCheckout('{{d.PROC_INS_ID}}')">
        <i class="iconfont" style="color: #c20000;">&#xe6b6;</i>
    </a>
   <!-- <a class="table-btn" type="button" title="删除" onclick="deleteSingleItem('{{d.endAccountAttrId}}', '{{d.PROJECT_NAME}}')">
        <i class="iconfont" style="color: #c20000;">&#xe7f3;</i>
    </a>-->
</script>

<style>
    .table-btn {
        margin-right: 5px;
    }

    .layui-btn-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
    }



    /* 按钮组间距 */
    .layui-btn-sm {
        margin-right: 8px;
    }

    .layui-btn-sm:last-child {
        margin-right: 0;
    }


</style>

<script type="text/javascript">
    layui.use(['jqdate', 'jqform', 'jqfrm', 'jqbind', 'table', 'layer', 'jquery'], function () {
        var $ = layui.jquery;
        var frm = layui.jqfrm;
        var form = layui.jqform;
        var jqbind = layui.jqbind;
        var layer = layui.layer;
        var table = layui.tableNew;
        var ctx = top.global.ctx;
        var laydate = layui.laydate;
        var tpl = layui.laytpl;
        var element = layui.element;

        // 数据存储对象
        var dataBox = {
            checkedUnit: {
                toShow: [],
                toSend: [],
                provCodes: [], // 省分编码
                areaCodes: []  // 地市编码
            },
            aProfession: {
                toShow: [],
                toSend: []
            }
        };

        /*****************************查询表格数据***********************************/
        //获取查询参数
        window.getParams = function () {
            var params = {
             reportId1: 200024,
             reportId2: 200025
            };

            // 审计完成时间
            var auditCompleteTime = $("#auditCompleteTime").val();
            if (auditCompleteTime) {
                params.auditCompDateStart = auditCompleteTime.substr(0, 6);
                params.auditCompDateEnd = auditCompleteTime.substr(9);
            }
           
            // 被检查单位（区分省分和地市）
            if (dataBox.checkedUnit.provCodes && dataBox.checkedUnit.provCodes.length > 0) {
                params.reportProvCode = dataBox.checkedUnit.provCodes;
            }

            if (dataBox.checkedUnit.areaCodes && dataBox.checkedUnit.areaCodes.length > 0) {
                params.reportAreaCode = dataBox.checkedUnit.areaCodes;
            }

            // A级专业
            if (dataBox.aProfession.toSend.length > 0) {
                params.specName = dataBox.aProfession.toSend;
            }

            // 项目ERP编号
            var projectErpCode = $("#projectErpCode").val();
            if (projectErpCode) {
                params.projectCodeErp = projectErpCode;
            }

            // 合同/订单编号
            var contractOrderCode = $("#contractOrderCode").val();
            if (contractOrderCode) {
                params.contractOrder = contractOrderCode;
            }

            // 原审计机构名称
            var originalAuditOrgName = $("#originalAuditOrgName").val();
            if (originalAuditOrgName) {
                params.auditUnit = originalAuditOrgName;
            }

            // 追责主键（从上一个页面传过来）
            var accountabilityId = getUrlParam('accountabilityId');
            if (accountabilityId) {
                params.accountabilityId = accountabilityId;
            }

            // 项目名称
            var projectName = $("#projectName").val();
            if (projectName) {
                params.projectName = projectName;
            }

            // 施工单位
            var constructionUnit = $("#constructionUnit").val();
            if (constructionUnit) {
                params.constructionUnit = constructionUnit;
            }

            // 台账账期区间
            var ledgersMonth = $("#ledgersMonth").val();
            if (ledgersMonth) {
                params.ledgersMonthStart = ledgersMonth.substr(0, 6);
                params.ledgersMonthEnd = ledgersMonth.substr(9);
            }

            // 过滤掉空数组和空值，避免发送无效的查询条件到后端
            var filteredParams = {};
            for (var key in params) {
                if (params.hasOwnProperty(key)) {
                    var value = params[key];
                    // 保留非空值，对于数组类型，只保留非空数组
                    if (value !== null && value !== undefined && value !== '') {
                        if (Array.isArray(value)) {
                            if (value.length > 0) {
                                filteredParams[key] = value;
                            }
                        } else {
                            filteredParams[key] = value;
                        }
                    }
                }
            }

            return filteredParams;
        }
        //查询数据
        window.queryOverallAccountabilityTableInfo = function () {
            console.log('开始查询项目筛选列表...');

            // 直接调用渲染表格函数，使用后端分页
            renderTableWithData();
        }

        // 渲染表格数据的函数
        function renderTableWithData() {

          var tableHeader = [
                {
                    "colspan": "0",
                    "field": "EXTRACT_MONTH",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align: center;",
                    "title": "台账账期",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "REPORT_PROV_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align: center;",
                    "title": "报审省份",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "REPORT_AREA_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align: center;",
                    "title": "报审地市",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "PROJECT_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "项目名称",
                    "width": "400"
                },
                {
                    "colspan": "0",
                    "field": "PROJECT_CODE_ERP",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "项目ERP编码",
                    "width": "150"
                },
                {
                    "colspan": "0",
                    "field": "SPEC_A_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "A级专业名称",
                    "width": "140"
                },
                {
                    "colspan": "0",
                    "field": "SPEC_B_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "B级专业名称",
                    "width": "140"
                },
                {
                    "colspan": "0",
                    "field": "SPEC_C_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "C级专业名称",
                    "width": "140"
                },

                {
                    "colspan": "0",
                    "field": "DESIGN_CODE",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "设计批复文号",
                    "width": "170"
                },
                {
                    "colspan": "0",
                    "field": "DESIGN_INVEST",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:right",
                    "title": "批复概算投资额",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "DESIGN_BUILD_INVEST",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:right",
                    "title": "其中：建安投资额",
                    "width": "150"
                },
                {
                    "colspan": "0",
                    "field": "AUDIT_TYPE_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "项目差异化审计类型",
                    "width": "280"
                },

                {
                    "colspan": "0",
                    "field": "CONSTRUCTION_UNIT",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "施工单位",
                    "width": "200"
                },
                {
                    "colspan": "48",
                    "field": "JSSJXX",
                    "rowspan": "1",
                    "spanStartRow": "0",
                    "style": "text-align:center",
                    "title": "结算审计信息",
                },
                {
                    "colspan": "0",
                    "field": "REPORT_FINAL_UNIT_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "出具审计报告单位名称",
                    "width": "200"
                },



                {
                    "colspan": "0",
                    "field": "AUDIT_REPORT_CODE",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "审计报告编号",
                    "width": "200"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_REPORT_NAME",
                    "rowspan": "3",
                    "spanStartRow": "0",
                    "style": "text-align:left",
                    "title": "审计报告名称",
                    "width": "200"
                },

                {
                    "colspan": "0",
                    "field": "CONTRACT_CODE",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align:left",
                    "title": "合同/协议编号",
                    "width": "200"
                },
                {
                    "colspan": "0",
                    "field": "ORDER_ID",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align:left",
                    "title": "订单编号",
                    "width": "200"
                },
                {
                    "colspan": "0",
                    "field": "ERP_ORDER_ID",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align:left",
                    "title": "合同订单名称",
                    "width": "200"
                },
                {
                    "colspan": "0",
                    "field": "COMORDER_AMOUNT",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align:right",
                    "title": "合同/订单金额（不含税）",
                    "width": "200"
                },


                {
                    "colspan": "0",
                    "field": "REPORT_ORG_NAME",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align:left",
                    "title": "报审部门",
                    "width": "18%"
                },
                {
                    "colspan": "0",
                    "field": "REPORT_PERSON",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "报审人",
                    "width": "130"
                },

                {
                    "colspan": "0",
                    "field": "END_ACCOUNT_CODE",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计工单号/批次号",
                    "width": "130",
                    "templet": function(d) {
                        var endAccountCode = d.END_ACCOUNT_CODE || '';
                        var auditBatchCode = d.AUDIT_BATCH_CODE || '';

                        // 如果两个字段都有值，用/分隔
                        if (endAccountCode && auditBatchCode) {
                            return endAccountCode + '/' + auditBatchCode;
                        }
                        // 如果只有一个字段有值，直接显示
                        else if (endAccountCode) {
                            return endAccountCode;
                        }
                        else if (auditBatchCode) {
                            return auditBatchCode;
                        }
                        // 如果都没有值，显示空
                        else {
                            return '';
                        }
                    }
                },
                {
                    "colspan": "6",
                    "field": "",
                    "rowspan": "1",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "送审金额(元)",
                    "width": "10%"
                },
                {
                    "colspan": "6",
                    "field": "",
                    "rowspan": "1",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审定金额(元)",
                    "width": "10%"
                },
                {
                    "colspan": "6",
                    "field": "",
                    "rowspan": "1",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审减金额(元)",
                    "width": "10%"
                },
                {
                    "colspan": "6",
                    "field": "",
                    "rowspan": "1",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审减率（%）",
                    "width": "10%"
                },
                {
                    "colspan": "7",
                    "field": "",
                    "rowspan": "1",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计费（元）",
                    "width": "10%"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_SUBMIT_DATE",
                    "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计开始时间",
                    "width": "120"
                },
                {
                    "colspan": "0",
                    "field": "AUDIT_SUBMIT_DATE",
                     "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计结果提交时间",
                    "width": "120"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_PERSON_NAME",
                      "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "接审人",
                    "width": "120"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_USER_NAME",
                     "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计人员",
                    "width": "120"
                },

                {
                    "colspan": "0",
                    "field": "ENTRUST_USER_NAME",
                   "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "委托联系人",
                    "width": "120"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_TYPE_ID",
                      "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计方式",
                    "width": "120"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_UNIT",
                     "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计中介机构名称",
                    "width": "120"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_LIABLE_USER_NAME",
                      "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计组负责人",
                    "width": "120"
                },

                {
                    "colspan": "0",
                    "field": "AUDIT_LEADER_USER",
                     "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计组组长",
                    "width": "120"
                },


                {
                    "colspan": "0",
                    "field": "AUDIT_CREW_USER",
                      "rowspan": "2",
                    "spanStartRow": "1",
                    "style": "text-align: center;",
                    "title": "审计组组员",
                    "width": "120"
                },
                {
                    "colspan": "0",
                    "field": "SEND_AMOUNT_EXECUTION_COST",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "施工费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "SEND_AMOUNT_MATERIALS_COST_B",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其中:乙供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "SEND_AMOUNT_MATERIALS_COST_A",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "SEND_AMOUNT_EQUIT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供设备费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "SEND_AMOUNT_OTHER",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其它费用",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "SEND_AMOUNT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "合计",
                    "width": "130"
                },


                {
                    "colspan": "0",
                    "field": "FIX_AMOUNT_EXECUTION_COST",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "施工费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "FIX_AMOUNT_MATERIALS_COST_B",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其中:乙供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "FIX_AMOUNT_MATERIALS_COST_A",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "FIX_AMOUNT_EQUIT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供设备费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "FIX_AMOUNT_OTHER",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其它费用",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "FIX_AMOUNT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "合计",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_AMOUNT_EXECUTION_COST",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "施工费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_AMOUNT_MATERIALS_COST_B",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其中:乙供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_AMOUNT_MATERIALS_COST_A",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_AMOUNT_EQUIT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供设备费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_AMOUNT_OTHER",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其它费用",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_AMOUNT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "合计",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_RATE_EXECUTION_COST",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "施工费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_RATE_MATERIALS_COST_B",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其中:乙供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_RATE_MATERIALS_COST_A",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供材料费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_RATE_EQUIT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "甲供设备费",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_RATE_OTHER",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "其它费用",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "CUT_RATE",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "合计",
                    "width": "130"
                },





                {
                    "colspan": "0",
                    "field": "BASE_COST",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "基本收费",
                    "width": "130"
                },

                {
                    "colspan": "0",
                    "field": "BENEFIT_COST",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "效益收费",
                    "width": "130"
                },

                {
                    "colspan": "0",
                    "field": "COST_CHECK_CHANGE",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "审计调整金额",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "COST_WITH_TAX",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "含税",
                    "width": "130"
                },
                {
                    "colspan": "0",
                    "field": "COST_OUT_TAX",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "不含税",
                    "width": "130"
                },

                {
                    "colspan": "0",
                    "field": "COST_TAX_AMOUNT",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "税金",
                    "width": "130"
                },


                {
                    "colspan": "0",
                    "field": "COST_RATE",
                    "rowspan": "1",
                    "spanStartRow": "2",
                    "style": "text-align:right",
                    "title": "税率%",
                    "width": "130"
                },
            ]











            // 使用tableHeader数据动态生成表格配置
            var tableData = [
                [],
                [],
                []
            ];

            // 添加复选框列
            tableData[0].push({
                type: 'checkbox',
                title: '选择',
                rowspan: 3,
                width: 60,
                fixed: 'left'
            });

            // 添加序号列
            tableData[0].push({
                type: 'numbers',
                title: '序号',
                rowspan: 3,
                width: 80,
                fixed: 'left'
            });

            // 处理tableHeader数据
            $.each(tableHeader, function (index, obj) {
                if (Number(obj.spanStartRow) == 0) {
                    // 第一行的列
                    var align = 'center';
                    if (obj.style && obj.style.indexOf('left') > -1) {
                        align = 'left';
                    } else if (obj.style && obj.style.indexOf('right') > -1) {
                        align = 'right';
                    }

                    tableData[0].push({
                        field: obj.field,
                        title: obj.title,
                        align: align,
                        width: obj.width,
                        colspan: Number(obj.colspan),
                        rowspan: Number(obj.rowspan)
                    });
                } else if (Number(obj.spanStartRow) == 1) {
                    // 第二行的列
                    var align = 'center';
                    if (obj.style && obj.style.indexOf('left') > -1) {
                        align = 'left';
                    } else if (obj.style && obj.style.indexOf('right') > -1) {
                        align = 'right';
                    }

                    tableData[1].push({
                        field: obj.field,
                        title: obj.title,
                        align: align,
                        width: obj.width,
                        colspan: Number(obj.colspan),
                        rowspan: Number(obj.rowspan)
                    });
                } else if (Number(obj.spanStartRow) == 2) {
                    // 第二行的列
                    var align = 'center';
                    if (obj.style && obj.style.indexOf('left') > -1) {
                        align = 'left';
                    } else if (obj.style && obj.style.indexOf('right') > -1) {
                        align = 'right';
                    }

                    tableData[2].push({
                        field: obj.field,
                        title: obj.title,
                        align: align,
                        width: obj.width,
                        colspan: Number(obj.colspan),
                        rowspan: Number(obj.rowspan)
                    });
                }
            });

            // 添加操作列
            tableData[0].push({
                title: '操作',
                rowspan: 3,
                width: 150,
                align: 'center',
                templet: '#configBar',
                fixed: 'right'
            });

            // 渲染表格
            table.render({
                elem: '#accountabilityTable',
                id: 'accountabilityTable',
                url: ctx + '/prj/accountabilityfilter/queryProjectFilterList',  // 使用后端分页
                where: getParams(),  // 查询条件
                page: true,
                even: true,
                limits: [10, 30, 60, 120, 300],
                limit: 10,
                height: 'full-200',
                cols: tableData,
                done: function (res) {
                    // 表头颜色一致
                    $('.layui-table-header .layui-table').removeAttr("lay-even");
                    $('.layui-table-header .layui-table').attr("class", 'layui-table lay-even');

                    // 更新按钮状态
                    updateAddExtractAuditButtonStatus();

                    console.log('表格渲染完成，数据条数：', res.data ? res.data.length : 0);
                }
            })
        }
        /*****************************查询表格数据***********************************/
        /*********************初始化查询参数***************************/
        window.initParamsList = function () {
            // 从URL参数中获取审计时间区间并设置到台账账期区间
            var checkMonth = getUrlParam('checkMonth');
            if (checkMonth) {
                $("#ledgersMonth").val(checkMonth);
            }

            //查询数据列表
            queryOverallAccountabilityTableInfo();


            //执行审结时间laydate实例
            laydate.render({
                elem: '#auditCompleteTime', //指定元素
                type: 'month',
                range: true,
                format: 'yyyyMM',
                isInitValue: false,
                showBottom: true,
                change: function (value, date, endDate) {
                    $("#auditCompleteTime").val(value);

                },
            });

            //执行台账账期区间laydate实例
            laydate.render({
                elem: '#ledgersMonth', //指定元素
                type: 'month',
                range: true,
                format: 'yyyyMM',
                isInitValue: false,
                showBottom: true,
                change: function (value, date, endDate) {
                    $("#ledgersMonth").val(value);

                },
            });
        }
        //查询
        window.reloadTable = function () {
            // 重新加载表格，会自动使用新的查询条件
            queryOverallAccountabilityTableInfo()
        }

        initParamsList();
        /*********************初始化查询参数***************************/



        /*****************************填报整体追责***********************************/
        window.fillingBtn = function (accountabilityId) {

        }
        /*****************************填报整体追责***********************************/

        //整体追责查看台账明细
        window.showCheckLedgersList = function (detailId) {
            var index = window.parent.layer.open({
                type: 2,
                maxmin: true,
                content: 'views/audit/prj/endaccount/endreauditstatic/checkLedgersList.html?accountabilityDetailId=' + detailId
                    + '&type=accountability',
                title: [
                    '再审计台账', 'font-size:14px;background-color: #f2f2f2;'
                ],
                area: ['95%', '98%'],
                fixed: true,
                success: function (layero, index) {
                },
                end: function () {
                }
            });
        }

        // 初始化查询条件
        function initFilterOptions() {
            console.log('开始初始化查询条件选项...');
            $.ajax({
                url: ctx + '/prj/accountabilityfilter/queryProjectFilterParams',
                type: 'POST',
                dataType: 'JSON',
                contentType: 'application/json;charset=UTF-8',
                success: function (res) {
                    console.log('查询条件接口响应：', res);
                    if (res.httpCode == 200 && res.data) {
                        console.log('使用接口数据初始化选项');
                        // 初始化被检查单位选项
                        initCheckedUnitOptions(res.data.sendProvList);

                        // 初始化A级专业选项
                        initAProfessionOptions(res.data.professionList);
                    } else {
                        console.error('初始化查询条件失败：', res.msg);
                        console.log('使用默认选项');
                        // 使用默认选项
                        initCheckedUnitOptionsDefault();
                        initAProfessionOptionsDefault();
                    }
                },
                error: function (xhr, status, error) {
                    console.error('网络异常，初始化查询条件失败：', error);
                    console.log('使用默认选项');
                    // 使用默认选项
                    initCheckedUnitOptionsDefault();
                    initAProfessionOptionsDefault();
                }
            });
        }

        // 初始化被检查单位选项（从接口数据）
        function initCheckedUnitOptions(sendProvList) {
            console.log('初始化被检查单位选项，数据：', sendProvList);
            var html = '';
            if (sendProvList && sendProvList.length > 0) {
                $.each(sendProvList, function (index, item) {
                    // 根据数据结构判断显示省分还是地市
                    var displayName = '';
                    var codeValue = '';
                    var dataType = ''; // 标记是省分还是地市

                    if (item.provCode && item.provName) {
                        // 有省分信息
                        displayName = item.provName;
                        codeValue = item.provCode;
                        dataType = 'prov';
                    }

                    if (item.areaCode && item.areaName) {
                        // 有地市信息，优先显示地市
                        displayName = item.areaName;
                        codeValue = item.areaCode;
                        dataType = 'area';
                    }

                    if (displayName && codeValue) {
                        html += '<div class="endStatus" data-code="' + codeValue + '" data-type="' + dataType + '" onclick="checkedUnitClick(this)">' + displayName + '</div>';
                    }
                });
                console.log('被检查单位HTML：', html);
            } else {
                console.log('没有被检查单位数据，使用默认选项');
                // 如果没有数据，使用默认选项
                initCheckedUnitOptionsDefault();
                return;
            }

            $('#checkedUnit').html(html);
            console.log('被检查单位选项初始化完成');
        }

        // 初始化A级专业选项（从接口数据）
        function initAProfessionOptions(professionList) {
            console.log('初始化A级专业选项，数据：', professionList);
            var html = '';
            if (professionList && professionList.length > 0) {
                $.each(professionList, function (index, item) {
                    // 专业数据结构：code 和 codeText
                    var code = item.code;
                    var name = item.codeText;

                    if (code && name) {
                        html += '<div class="endStatus" data-code="' + code + '" onclick="aProfessionClick(this)">' + name + '</div>';
                    }
                });
                console.log('A级专业HTML：', html);
            } else {
                console.log('没有A级专业数据，使用默认选项');
                // 如果没有数据，使用默认选项
                initAProfessionOptionsDefault();
                return;
            }

            $('#aProfession').html(html);
            console.log('A级专业选项初始化完成');
        }

        // 默认被检查单位选项（备用）
        function initCheckedUnitOptionsDefault() {
            console.log('使用默认被检查单位选项');
            var checkedUnitOptions = [
                { code: '省级', name: '省级' },
                { code: '市级', name: '市级' },
                { code: '县级', name: '县级' },
                { code: '乡镇', name: '乡镇' },
                { code: '村级', name: '村级' },
                { code: '其他', name: '其他' }
            ];

            var html = '';
            $.each(checkedUnitOptions, function (index, item) {
                html += '<div class="endStatus" data-code="' + item.code + '" onclick="checkedUnitClick(this)">' + item.name + '</div>';
            });

            $('#checkedUnit').html(html);
            console.log('默认被检查单位选项设置完成，HTML：', html);
        }

        // 默认A级专业选项（备用）
        function initAProfessionOptionsDefault() {
            console.log('使用默认A级专业选项');
            var aProfessionOptions = [
                { code: '房建', name: '房建' },
                { code: '市政', name: '市政' },
                { code: '公路', name: '公路' },
                { code: '水利', name: '水利' },
                { code: '电力', name: '电力' },
                { code: '通信', name: '通信' },
                { code: '铁路', name: '铁路' },
                { code: '港航', name: '港航' },
                { code: '机场', name: '机场' },
                { code: '石化', name: '石化' },
                { code: '冶金', name: '冶金' },
                { code: '机电', name: '机电' }
            ];

            var html = '';
            $.each(aProfessionOptions, function (index, item) {
                html += '<div class="endStatus" data-code="' + item.code + '" onclick="aProfessionClick(this)">' + item.name + '</div>';
            });

            $('#aProfession').html(html);
            console.log('默认A级专业选项设置完成，HTML：', html);
        }

        // 被检查单位点击事件
        window.checkedUnitClick = function (element) {
            var $element = $(element);
            var code = $element.data('code');
            var type = $element.data('type'); // 获取数据类型（prov或area）
            var name = $element.text();

            if ($element.hasClass('haschoose')) {
                // 取消选择
                $element.removeClass('haschoose');

                // 从对应的数组中移除
                if (type === 'prov') {
                    var index = dataBox.checkedUnit.provCodes.indexOf(code);
                    if (index > -1) {
                        dataBox.checkedUnit.provCodes.splice(index, 1);
                    }
                } else if (type === 'area') {
                    var index = dataBox.checkedUnit.areaCodes.indexOf(code);
                    if (index > -1) {
                        dataBox.checkedUnit.areaCodes.splice(index, 1);
                    }
                }

                // 从显示数组中移除
                var showIndex = dataBox.checkedUnit.toShow.indexOf(name);
                if (showIndex > -1) {
                    dataBox.checkedUnit.toShow.splice(showIndex, 1);
                }

                // 从发送数组中移除
                var sendIndex = dataBox.checkedUnit.toSend.indexOf(code);
                if (sendIndex > -1) {
                    dataBox.checkedUnit.toSend.splice(sendIndex, 1);
                }
            } else {
                // 选择
                $element.addClass('haschoose');

                // 添加到对应的数组中
                if (type === 'prov') {
                    if (!dataBox.checkedUnit.provCodes) {
                        dataBox.checkedUnit.provCodes = [];
                    }
                    dataBox.checkedUnit.provCodes.push(code);
                } else if (type === 'area') {
                    if (!dataBox.checkedUnit.areaCodes) {
                        dataBox.checkedUnit.areaCodes = [];
                    }
                    dataBox.checkedUnit.areaCodes.push(code);
                }

                dataBox.checkedUnit.toSend.push(code);
                dataBox.checkedUnit.toShow.push(name);
            }

            // 更新显示文本
            updateCheckedUnitDisplay();
        }

        // A级专业点击事件
        window.aProfessionClick = function (element) {
            var $element = $(element);
            var code = $element.data('code');
            var name = $element.text();

            if ($element.hasClass('haschoose')) {
                // 取消选择
                $element.removeClass('haschoose');
                var index = dataBox.aProfession.toSend.indexOf(code);
                if (index > -1) {
                    dataBox.aProfession.toSend.splice(index, 1);
                    dataBox.aProfession.toShow.splice(index, 1);
                }
            } else {
                // 选择
                $element.addClass('haschoose');
                dataBox.aProfession.toSend.push(code);
                dataBox.aProfession.toShow.push(name);
            }

            // 更新显示文本
            updateAProfessionDisplay();
        }

        // 更新被检查单位显示文本
        function updateCheckedUnitDisplay() {
            var $container = $('.checkedUnit-selector');
            if (dataBox.checkedUnit.toShow.length > 0) {
                var displayText = dataBox.checkedUnit.toShow.join('、');
                $container.addClass('havec');
                $container.find('.show_text').text(displayText).attr('title', displayText);
                $container.find('.to_clean').show();
            } else {
                $container.removeClass('havec');
                $container.find('.show_text').text('请选择').removeAttr('title');
                $container.find('.to_clean').hide();
            }
        }

        // 更新A级专业显示文本
        function updateAProfessionDisplay() {
            var $container = $('.aProfession-selector');
            if (dataBox.aProfession.toShow.length > 0) {
                var displayText = dataBox.aProfession.toShow.join('、');
                $container.addClass('havec');
                $container.find('.show_text').text(displayText).attr('title', displayText);
                $container.find('.to_clean').show();
            } else {
                $container.removeClass('havec');
                $container.find('.show_text').text('请选择').removeAttr('title');
                $container.find('.to_clean').hide();
            }
        }

        // 清除选择项函数
        window.cleanItem = function (type) {
            if (type === 'checkedUnit') {
                $('#checkedUnit .endStatus').removeClass('haschoose');
                $('.checkedUnit-selector').removeClass('havec');
                $('.checkedUnit-selector .show_text').text('请选择').removeAttr('title');
                $('.checkedUnit-selector .to_clean').hide();
                dataBox.checkedUnit.toSend = [];
                dataBox.checkedUnit.toShow = [];
                dataBox.checkedUnit.provCodes = [];
                dataBox.checkedUnit.areaCodes = [];
            } else if (type === 'aProfession') {
                $('#aProfession .endStatus').removeClass('haschoose');
                $('.aProfession-selector').removeClass('havec');
                $('.aProfession-selector .show_text').text('请选择').removeAttr('title');
                $('.aProfession-selector .to_clean').hide();
                dataBox.aProfession.toSend = [];
                dataBox.aProfession.toShow = [];
            }
        }

        // 重置表单函数
        window.resetForm = function () {
            // 重置普通表单元素
            document.getElementById('accountabilityForm').reset();

            // 清空自定义选择器
            cleanItem('checkedUnit');
            cleanItem('aProfession');

            // 强制清空 dataBox 数据，确保重置彻底
            dataBox.checkedUnit = {
                toShow: [],
                toSend: [],
                provCodes: [],
                areaCodes: []
            };
            dataBox.aProfession = {
                toShow: [],
                toSend: []
            };
            // 添加调试信息
            console.log('重置后的 dataBox:', dataBox);
            console.log('重置后的查询参数:', getParams());

            // 重新加载表格（使用空的查询条件）
            queryOverallAccountabilityTableInfo()
        }

        // 获取筛选结果的方法，供父页面调用
        window.getFilterResult = function () {
            // 获取选中的表格数据
            var checkStatus = table.checkStatus('accountabilityTable');
            var selectedData = checkStatus.data;

            if (selectedData.length === 0) {
                layer.msg('请至少选择一条记录！', {icon: 2});
                return null;
            }

            // 获取筛选条件
            var filterConditions = {
                auditCompleteTime: $("#auditCompleteTime").val(),
                ledgersMonth: $("#ledgersMonth").val(),
                checkedUnit: {
                    selected: dataBox.checkedUnit.toShow,
                    codes: dataBox.checkedUnit.toSend
                },
                aProfession: {
                    selected: dataBox.aProfession.toShow,
                    codes: dataBox.aProfession.toSend
                }
            };

            // 返回筛选结果
            return {
                selectedData: selectedData,
                filterConditions: filterConditions,
                selectedCount: selectedData.length
            };
        }

        // 获取URL参数的函数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        // 更新添加抽查按钮状态
        function updateAddExtractAuditButtonStatus() {
            // 监听复选框变化
            table.on('checkbox(accountabilityTable)', function (obj) {
                var checkStatus = table.checkStatus('accountabilityTable');
                var data = checkStatus.data;

                if (data.length > 0) {
                    $('#fillBtn').removeClass('layui-btn-disabled').removeAttr('disabled');
                } else {
                    $('#fillBtn').addClass('layui-btn-disabled').attr('disabled', 'disabled');
                }
            });

            // 初始状态设置按钮为禁用
            $('#fillBtn').addClass('layui-btn-disabled').attr('disabled', 'disabled');
        }

        // 添加抽查方法
        window.addExtractAudit = function () {
            // 检查按钮是否被禁用
            if ($('#fillBtn').hasClass('layui-btn-disabled')) {
                layer.msg('请先选择要添加的记录！', { icon: 2 });
                return;
            }
            // 获取选中的行数据
            var checkStatus = table.checkStatus('accountabilityTable');
            var data = checkStatus.data;

            if (data.length === 0) {
                layer.msg('请至少选择一条记录！', { icon: 2 });
                return;
            }

            // 获取选中的初审台账表主键
            var endLedgersIds = [];
            $.each(data, function (index, item) {
                if (item.id) {
                    endLedgersIds.push(item.id);
                } else if (item.ID) {
                    endLedgersIds.push(item.ID);
                } else if (item.LEDGER_ID) {
                    endLedgersIds.push(item.LEDGER_ID);
                }
            });

            if (endLedgersIds.length === 0) {
                layer.msg('获取选中记录ID失败！', { icon: 2 });
                return;
            }

            // 获取追责主键
            var accountabilityId = getUrlParam('accountabilityId');
            if (!accountabilityId) {
                layer.msg('缺少追责主键参数！', { icon: 2 });
                return;
            }

            // 确认提示
            layer.confirm('确定要添加选中的 ' + data.length + ' 条记录到抽查吗？', {
                icon: 3,
                title: '确认添加'
            }, function (index) {
                // 发送请求
                $.ajax({
                    url: ctx + '/prj/accountabilityfilter/saveProjectFilterInfo',
                    type: 'POST',
                    dataType: 'JSON',
                    contentType: 'application/json;charset=UTF-8',
                    data: JSON.stringify({
                        accountabilityId: accountabilityId,
                        endLedgersIds: endLedgersIds
                    }),
                    success: function (res) {
                        if (res.httpCode == 200) {
                            layer.msg('添加抽查成功！', { icon: 1 });
                            // 刷新表格
                            table.reload('accountabilityTable', {
                                where: getParams()
                            });
                            // 关闭确认框
                            layer.close(index);
                        } else {
                            layer.msg(res.msg || '添加抽查失败！', { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('网络异常，添加抽查失败！', { icon: 2 });
                    }
                });
            });
        }




        // // 单个删除方法
        // window.deleteSingleItem = function(endAccountAttrId, projectName) {
        //     if (!endAccountAttrId) {
        //         layer.msg('缺少删除参数！', {icon: 2});
        //         return;
        //     }
        //
        //     layer.confirm('确定要删除项目"' + (projectName || '该项目') + '"吗？', {
        //         icon: 3,
        //         title: '确认删除'
        //     }, function(index) {
        //         $.ajax({
        //             url: ctx + '/prj/accountabilityfilter/delProjectFilterInfo',
        //             type: 'POST',
        //             dataType: 'JSON',
        //             contentType: 'application/json;charset=UTF-8',
        //             data: JSON.stringify({
        //                 endAccountAttrId: endAccountAttrId
        //             }),
        //             success: function(res) {
        //                 if (res.httpCode == 200) {
        //                     layer.msg('删除成功！', {icon: 1});
        //                     // 刷新表格
        //                     queryOverallAccountabilityTableInfo();
        //                     // 关闭确认框
        //                     layer.close(index);
        //                 } else {
        //                     layer.msg(res.msg || '删除失败！', {icon: 2});
        //                 }
        //             },
        //             error: function() {
        //                 layer.msg('网络异常，删除失败！', {icon: 2});
        //             }
        //         });
        //     });
        // }













        // 初始化查询条件选项
        initFilterOptions();



    });
</script>

</html>
