<!--
风研成果应用审核
-->
<!--#include virtual ="include/header.html"-->
<link href="resource/css/audit/online/tagmodel/tagCommon.css?v=6.5" rel="stylesheet" type="text/css">
<style>
    .layui-form-label {
        width: 140px !important;
    }
    .layui-laydate .laydate-btns-clear{
        display: inline-block;
    }
    .layui-input-block {
        margin-left: 140px !important;
    }

    .float-left {
        float: left;
    }
    .layui-checkbox-disbaled{
        display: none;
    }
    /*thead .layui-table-cell .layui-form-checkbox {*/
    /*    display: none;*/
    /*}*/

    .float-right {
        float: right;
    }

    .layui-table-cell {
        height: auto;
        line-height: 20px;
        padding: 4px 15px;
    }




    @media only screen and (min-width: 1200px) and (max-width: 1440px) {


    }

    @media only screen and (min-width: 1440px) and (max-width: 1600px) {

    }

    .last-btn {
        width: 200px;
        flaot: right;
        text-align: right
    }

    .last-btn-one {
        display: inline-block;
        cursor: pointer;
        height: 32px;
        line-height: 30px;
        border: 1px solid #c20000;
        box-sizing: border-box;
        border-radius: 4px;
        padding: 0px 8px;
    }

    .last-btn-one .iconfont {
        color: #c20000;
        font-size: 16px;
        margin-right: 4px;
    }

    .last-btn-one span:last-child {
        font-size: 14px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #c20000;
    }

    .last-btn-one:last-child {
        margin-right: 0px;
    }

    .layui-col-space10 > * {
        padding: 0;
    }

    .layui-table tr td .layui-table-cell {
        height: auto;
        white-space: break-spaces;
    }
</style>
<body>
<div class="layui-fluid larry-wrapper">
    <section class="panel panel-padding">
        <form class="layui-form layui-form-pane form-conmon form-conmon-more"
              data-params='{"dataName":"matterTable", "action":"list", "ajax":false, "bind":false}' id="matterSearch">
            <div class="layui-form-item layui-form-item-sm new-style">
                <input id="processDefinitionKey" name="processDefinitionKey" type="hidden" value="RiskResearchApply"/>
                <input id="controller" name="controller" type="hidden" value="/bdata/riskResearchApplyFlow"/>
                <div class="layui-row">
                    <div class=" layui-col-space10 float-left" style="width: calc(100% - 180px);padding-bottom:10px;">
                        <div class="layui-row layui-form">
                            <div class="layui-col-xs3">
                                <label class="layui-form-label">应用类型</label>
                                <div class="layui-input-block new-checkbox" >
                                    <input     name="atype" lay-skin="primary"
                                             title="问题" type="checkbox" value="point_common">
                                    <input   name="atype" lay-skin="primary"
                                            title="通报" type="checkbox" value="other">
                                </div>
                            </div>
                            <div class="layui-col-xs3">
                                <label class="layui-form-label">风研年度</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="remoteYear" name="remoteYear" placeholder="请选择"
                                           readonly
                                           type="text"/>
                                </div>
                            </div>
                            <div class="layui-col-xs3">
                                <label class="layui-form-label">来源</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="source" name="source" placeholder="请输入"
                                           type="text"/>
                                </div>
                            </div>
                            <div class="layui-col-xs3">
                                <label class="layui-form-label">团队</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" id="groupName" name="groupName" placeholder="请输入"
                                           type="text"/>
                                </div>
                            </div>

                        </div>
                        <div class="layui-col-md12 layui-col-sm12 refer-content" style="margin-top: 10px;">
                            <a class="btnMore" href="javascript:">
                                <span>更多搜索</span>
                                <i class="iconfont slide-down">&#xe603;</i>
                                <i class="iconfont slide-up noneJaudit">&#xe605;</i>
                            </a>
                        </div>
                        <div class="layui-row  search-more  noneJaudit" style="padding:0">
                            <div class="layui-form layui-row" style="margin-top:10px;">
                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">负责人/分析人</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="chargePersonName" name="chargePersonName"
                                               placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>
                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">专题</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="topicName" name="topicName" placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>
                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">子专题</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="subTopicName" name="subTopicName"
                                               placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>
                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">问题所属项目</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="projectName" name="projectName"
                                               placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>

                            </div>
                            <div class="layui-form layui-row" style="margin-top:10px;">
                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">问题/项目/通报编码</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="achievementCode" name="achievementCode"
                                               placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>
                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">问题/通报标题</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="achievementName" name="achievementName"
                                               placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>

                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">提交日期</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="startCreateTime" readonly name="startCreateTime"
                                               placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>

                                <div class="layui-col-xs3">
                                    <label class="layui-form-label">问题详情</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input" id="problemDescription" name="problemDescription"
                                               placeholder="请输入"
                                               type="text"/>
                                    </div>
                                </div>

                            </div>

                            <div class="layui-form layui-row" style="margin-top:10px;">
                                <div class="layui-col-xs6">
                                    <label class="layui-form-label">关联状态</label>
                                    <div class="layui-input-block new-checkbox" >
                                            <input   lay-filter="flowStatus" checked  name="flowStatus" lay-skin="primary"
                                                   title="待审批" type="checkbox" value="0">
                                            <input  lay-filter="flowStatus" name="flowStatus" lay-skin="primary"
                                                   title="审批通过" type="checkbox" value="1">
                                            <input   lay-filter="flowStatus" name="flowStatus" lay-skin="primary"
                                                   title="已删除" type="checkbox" value="2">
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                    <div class="float-right text-right" style="width:180px">
                        <button class="layui-btn layui-btn-sm" id="searchBtn" onclick="selectProblemList();"
                                type="button"><i class="iconfont search-icon">&#xe61b;</i> 查询
                        </button>
                        <button class="layui-btn layui-btn-danger layui-btn-sm" id="resetBtn"
                                onclick="resetSelectProblemList();" type="button">
                            <i class="iconfont search-icon">&#xe63a;</i> 重置
                        </button>

                    </div>
                </div>
                <div class="layui-col-md12 layui-col-sm12 layui-col-lg12 text-right">
                                        <div class="last-btn-one" onclick="integralProcessing()"  style="margin-bottom: 10px;display: inline-block">
                                            <i class="iconfont search-icon">&#xe627;</i><span>积分处理提醒</span>
                                        </div>
                    <div class="last-btn-one" id="addMatterInfo" onclick="addMatterInfo()"
                         style="margin-bottom: 10px;display: inline-block"><i class="iconfont search-icon">&#xe683;</i><span>批量审批通过</span>
                    </div>
                </div>
            </div>
        </form>
        <div class="layui-form">
            <table class="layui-table jq-even" id="matterTable" lay-filter="matterTable"></table>
        </div>
        <div class="layui-form width100" style="display: none;" id="deleteContent">
            <div style="padding: 20px 20px 0 20px;">
                <textarea id="deleteReason" maxlength="500" class="layui-textarea" placeholder="请输入删除原因" ></textarea>
                <div class="layui-form-item" style="margin-top:8px;margin-bottom: 0;">
                    <label class="layui-form-label" style="width: 92px !important;">是否通知提交人</label>
                    <div class="layui-input-block" style="margin-left:92px !important;">
                        <input id="sendMsg" lay-filter="sendMsg" lay-skin="switch" lay-text="是 |否 " name="sendMsg"
                               type="checkbox" value="0">
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
</body>
<!--#include virtual ="include/version.html"-->
<!--#include virtual ="include/tpl/select-cat.html"-->
<script id="toolBar" type="text/html">
    {{# if(d.flowStatus == '0'){ }}
    <a class="table-btn" lay-event="examine" title="审批通过"><i class="iconfont search-icon">&#xe683;</i></a>
    <a class="table-btn" lay-event="delete" title="删除"><i class="iconfont">&#xe7f3;</i></a>
    {{# } }}
</script>
<script type="text/javascript">
    var layer, ctx, $, form, frm;
    layui.use(['jqfrm', 'jqbind', 'laytpl', 'layer', 'upload', 'laydate', 'jqform'], function () {
        layer = layui.layer;
        ctx = top.global.ctx;
        form = layui.jqform;
        $ = layui.jquery;
        frm = layui.jqfrm;
        var tpl = layui.laytpl,
            jqbind = layui.jqbind,
            laydate = layui.laydate,
            table = layui.table,
            upload = layui.upload;
        var tableList = [];
        var project = false //项目
        var tongbao = false //通报
        var wenti = false //问题

        var myDate = new Date();
        var tYear = myDate.getFullYear();//当前年
        $('#remoteYear').val()

        //区间日期
        laydate.render({
            elem: '#startCreateTime',
            type: 'date',
            format: 'yyyy-MM-dd',
            range: true,
            done: function (value, date) {
                console.log(value)
            }
        });

        laydate.render({
            elem: "#remoteYear", //指定元素
            type: "year",
            range: false,
            format: "yyyy",
            done: function (value, date) {
            },
        });

        function getSelectedCheckboxValues(name) {
            // 获取所有 name 为指定名称的复选框
            var checkboxes = document.querySelectorAll('input[name="' + name + '"]:checked');
            // 创建一个数组来存储选中的值
            var selectedValues = [];

            // 遍历选中的复选框并获取它们的值
            checkboxes.forEach(function (checkbox) {
                selectedValues.push(checkbox.value);
            });

            return selectedValues;
        }

        var selectedValues = getSelectedCheckboxValues('a');


        // 查询 条件展开更多
        $(function () {
            $('.btnMore')
                .on(
                    'click',
                    function () {
                        var me = $(this), childDwon = me
                            .children('.slide-down'), childUp = me
                            .children('.slide-up');
                        if (childDwon.hasClass('none')) {
                            childDwon.removeClass('none');
                            childUp.addClass('none');
                            me.find('span').text("搜索更多");
                            $('.search-more').stop().hide();
                        } else {
                            childDwon.addClass('none');
                            childUp.removeClass('none');
                            me.find('span').text("收起更多");
                            $('.search-more').stop().show();
                        }
                    })

        });


        var currPage = 1;
        var table1;

        window.selectProblemList = function (page) {
            table = $.extend(table, {config: {checkName: 'checked'}});
            var selectedValues = getSelectedCheckboxValues('flowStatus');
            var atype = getSelectedCheckboxValues('atype');
            if(atype.length>1||atype.length==0){
                atype = null
            }else{
                atype = atype.toString()
            }
            var createTime = $('#startCreateTime').val();
            var startCreateTime = ''
            var endCreateTime = ''
            if(createTime){
                startCreateTime = createTime.split(' - ')[0]
                endCreateTime = createTime.split(' - ')[1]
            }
            table1 = table.render({
                elem: "#matterTable",
                url: ctx + "/bdata/leaderHomeDetail/queryAchievementReviewVo",
                even: true,
                limit: 10,
                page: true,
                height: 'full-150',
                where: {
                    'source': $('#source').val(),
                    'remoteYear': $("#remoteYear").val(),
                    'groupName': $("#groupName").val(),
                    'chargePersonName': $("#chargePersonName").val(),
                    'subTopicName': $("#subTopicName").val(),
                    'topicName': $("#topicName").val(),
                    'projectName': $("#projectName").val(),
                    'achievementCode': $("#achievementCode").val(),
                    'achievementName': $("#achievementName").val(),
                    'flowStatusList': selectedValues,
                    'problemDescription': $("#problemDescription").val(),
                    'atype': atype,
                    'startCreateTime':startCreateTime,
                    'endCreateTime':endCreateTime,
                },
                cols: [
                    [
                        {type: 'checkbox', width: '50'},
                        {type: 'numbers', width: '60', title: '序号', align: 'center'},
                        {field: 'atype', width: '85', title: '应用类型', align: 'center', style: 'text-align:left'},
                        {
                            field: 'achievementCode',
                            width: '114',
                            title: '问题/项目/</br>通报编码',
                            align: 'center',
                            style: 'text-align:left'
                        },
                        {
                            field: 'achievementName',
                            width: '160',
                            title: '问题/通报标题',
                            align: 'center',
                            style: 'text-align:left',
                            templet: function (d) {
                                if(d.previewFlag&&d.attachment){
                                    return '<a href="javascript:;" style="text-decoration: underline" class="text-red" onclick="openPreView(\''+d.attachment.id+'\',\''+htmlEncodeByRegExp(d.attachment.fileName)+'\')">'+d.achievementName+'</a>'
                                }else{
                                    return d.achievementName
                                }
                            }
                        },
                        {
                            field: 'problemDescription',
                            width: '300',
                            title: '问题详情',
                            align: 'center',
                            style: 'text-align:left'
                        },
                        {
                            field: 'projectName',
                            width: '120',
                            title: '问题所属项目',
                            align: 'center',
                            style: 'text-align:left'
                        },
                        {
                            field: 'dAchievementCode',
                            width: '120',
                            title: '风研编号',
                            align: 'center',
                            style: 'text-align:left'
                        },
                        {field: 'source', width: '80', title: '来源', align: 'center', style: 'text-align:left'},
                        {field: 'groupName', width: '80', title: '团队', align: 'center', style: 'text-align:left'},
                        {field: 'topicName', width: '120', title: '专题', align: 'center', style: 'text-align:left'},
                        {field: 'subTopicName', width: '120', title: '子专题', align: 'center', style: 'text-align:left'},
                        {
                            field: 'initialFocusRisk',
                            width: '300',
                            title: '聚焦风险',
                            align: 'center',
                            style: 'text-align:left'
                        },
                        {field: 'chargePersonName', width: '80', title: '负责人', align: 'center'},
                        {field: 'analystPersonName', width: '120', title: '分析人', align: 'center'},
                        {
                            field: 'flowStatus', width: '100', title: '关联状态', align: 'center', templet: function (d) {
                                if (d.flowStatus == "0")
                                    return '待审批';
                                else if (d.flowStatus == "1")
                                    return '审批通过';
                                else if (d.flowStatus == "2")
                                    return '已删除';
                            }
                        },
                        {field: 'createTime', width: '100', title: '提交时间', align: 'center'},
                        {title: '操作', align: 'center', width: '100', toolbar: '#toolBar'}
                    ],
                ],
                done: function (res, curr, count) {
                    currPage = res.page;
                    $(".layui-table th").css("borderColor", "#DDE4F1");
                    $(".layui-table-view .layui-table td").css("borderColor", "#DDE4F1");
                    $(".layui-table thead tr").css({
                        "background-color": " #F4F8FC",
                        color: "#333333",
                    });
                    tableList = res.data
                    if (res.data.length > 0) {
                        res.data[0]["LAY_CHECKED"] = false
                    }
                    //根据状态禁用checkbox 若状态为审批中和审批完成则禁用
                    for (let i = 0; i < tableList.length; i++) {
                        if (tableList[i].flowStatus != "0") {
                            $("tr[data-index='" + i + "']").find("input").prop('disabled', 'disabled');
                        }
                    }
                    form.render()
                },
            });
        }

        selectProblemList();
        // 批量审批通过
        window.addMatterInfo = function () {
            var dataList = layui.table.checkStatus('matterTable').data
            var dataListNew = []
            for (let i = 0; i < dataList.length; i++) {
                if (dataList[i].flowStatus == "0") {
                    dataListNew.push(dataList[i])
                }
            }
            if (dataListNew.length === 0) {
                frm.error('请选择数据！')
            } else {
                var ids = [];
                for (var i = 0; i < dataListNew.length; i++) {
                    ids.push(dataListNew[i].id)
                }
                layer.confirm('确认批量审批通过选择数据？', {icon: 3, title: '提示'}, function (index) {
                    $.ajax({
                        url: ctx + "/bdata/leaderHomeDetail/approveAchievementBatch"
                        , type: 'POST'
                        , dataType: 'JSON'
                        , data: JSON.stringify({
                            'FlowStatus': '1',
                            ids: ids
                        })
                        , contentType: "application/json;charset=UTF-8"
                        , success: function (res) {
                            if (res.httpCode == 200) {
                                layer.msg('提交成功', {icon: 1, time: 2000}, function () {
                                    layer.close(index);
                                });
                                selectProblemList();
                            } else {
                                frm.validate(res.msg);
                                layer.close(index);
                            }
                        }
                    });
                });
            }

        }

        // 积分处理提醒
        window.integralProcessing = function () {

            layer.confirm('确认发送发送积分处理提醒？', {icon: 3, title: '提示'}, function (index) {
                $.ajax({
                    url: ctx + "/bdata/leaderHomeDetail/integralProcessingReminder"
                    , type: 'POST'
                    , dataType: 'JSON'
                    , data: JSON.stringify({})
                    , contentType: "application/json;charset=UTF-8"
                    , success: function (res) {
                        if (res.httpCode == 200) {
                            layer.msg('提醒成功', {icon: 1, time: 2000}, function () {
                                layer.close(index);
                            });
                            selectProblemList();
                        } else {
                            frm.validate(res.msg);
                            layer.close(index);
                        }
                    }
                });
            });

        }


        //表格操作
        table.on('tool(matterTable)', function (obj) {
            var data = obj.data;
            if (obj.event === 'examine') {//审批
                data.commitFlag = 1;
                layer.confirm('确认审批通过该条数据？', {icon: 3, title: '提示'}, function (index) {
                    $.ajax({
                        url: ctx + "/bdata/leaderHomeDetail/approveAchievementSingle"
                        , type: 'POST'
                        , dataType: 'JSON'
                        , data: JSON.stringify({
                            "id": data.id,
                            "FlowStatus": "1"
                        })
                        , contentType: "application/json;charset=UTF-8"
                        , success: function (res) {
                            if (res.httpCode == 200) {
                                layer.msg('提交成功', {icon: 1, time: 2000}, function () {
                                    layer.close(index);
                                });
                                selectProblemList();
                            } else {
                                frm.validate(res.msg);
                                layer.close(index);
                            }
                        }
                    });
                });
            } else if (obj.event === 'adjust') {//调整
                var index = layer.open({
                    content: "views/audit/pro/projectmanagement/report/problemresearch.html?problemId=" + data.id + "&provCode=",
                    title: "关联风研成果",
                    area: ["95%", "85%"],
                    fixed: true,
                    type: 2,
                    end: function () {
                        loadProblemlist();
                    }
                });

            } else if (obj.event === 'delete') {//删除
                // 使用 layer.open 自定义弹窗内容
                layer.open({
                    type: 1, // 自定义弹窗类型
                    title: '删除原因',
                    area: ['400px', '227px'], // 弹窗宽高
                    content: $('#deleteContent'),
                    btn: ['确定', '取消'], // 弹窗按钮
                    yes: function (yesIndex) {
                        var reason = $('#deleteReason').val(); // 获取删除原因
                        var isNotice  = $('#sendMsg:checked').val()?'1':'0'; // 获取复选框状态

                        if (!reason) {
                            frm.error("请输入删除原因！");
                            return false; // 阻止关闭弹窗
                        }
                        $.ajax({
                            url: ctx + "/bdata/leaderHomeDetail/approveAchievementSingle"
                            , type: 'POST'
                            , dataType: 'JSON'
                            , data: JSON.stringify({
                                "id": data.id,
                                "FlowStatus": "2",
                                "reason":reason,
                                "isNotice":isNotice
                            })
                            , contentType: "application/json;charset=UTF-8"
                            , success: function (res) {
                                if (res.httpCode == 200) {
                                    layer.msg('删除成功', {icon: 1, time: 2000}, function () {
                                        layer.close(yesIndex);
                                    });
                                    selectProblemList();
                                } else {
                                    frm.validate(res.msg);
                                    layer.close(yesIndex);
                                }
                                $('#deleteReason').val('');
                                $('#deleteContent').hide()
                            }
                        });
                    },
                    btn2: function () {
                        // 取消按钮回调（可选）
                    }
                });
            }
            form.render();
        });

        function uncheckFlowStatusCheckboxes() {
            // 获取所有 name 为 flowStatus 的复选框
            var checkboxes = document.querySelectorAll('input[name="flowStatus"]');
            // 遍历这些复选框并取消选中
            checkboxes.forEach(function (checkbox) {
                checkbox.checked = false;
            });

            var checkboxes2 = document.querySelectorAll('input[name="atype"]');
            // 遍历这些复选框并取消选中
            checkboxes2.forEach(function (checkbox) {
                checkbox.checked = false;
            });
        }

        //重置按钮
        window.resetSelectProblemList = function () {
            $('#source').val('')
            $('#remoteYear').val('')
            $('#groupName').val('')
            $('#chargePersonName').val('')
            $('#subTopicName').val('')
            $('#topicName').val('')
            $('#projectName').val('')
            $('#achievementCode').val('')
            $('#achievementName').val('')
            $('#flowStatus').val('')
            $('#startCreateTime').val('')
            uncheckFlowStatusCheckboxes();
            form.render()
            selectProblemList()
        }
    });
</script>
</html>
