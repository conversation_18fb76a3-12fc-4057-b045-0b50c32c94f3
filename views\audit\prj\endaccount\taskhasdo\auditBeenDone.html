<!--#include virtual ="include/header.html"-->
<!--审计结算已办2.0-->
<body>
<div class="panel panel-padding">

    <!-- 项目信息面板 -->
    <div class="layui-collapse model-collapse queryCondition" lay-filter="projectInfoPanel">
        <div class="layui-colla-item model-item">
            <h2 class="layui-colla-title">
                <i class="iconfont">&#xe6e3;</i>项目信息
            </h2>
            <div class="layui-colla-content layui-show" id="projectInfoContent"></div>
        </div>
    </div>
    <!-- 管理信息面板 -->
    <div class="layui-collapse model-collapse queryCondition" lay-filter="managementInfoPanel">
        <div class="layui-colla-item model-item">
            <h2 class="layui-colla-title">
                <i class="iconfont">&#xe6a8;</i>管理信息
            </h2>
            <div class="layui-colla-content layui-show" id="managementInfoContent"></div>
        </div>
    </div>
    <!-- 报审信息面板 -->
    <div class="layui-collapse model-collapse queryCondition" lay-filter="auditInfoPanel">
        <div class="layui-colla-item model-item">
            <h2 class="layui-colla-title">
                <i class="iconfont">&#xe65c;</i>审计信息
            </h2>
            <div class="layui-colla-content layui-show" id="auditInfoContent"></div>
        </div>
    </div>
    <!--审计安排面板-->
    <div class="layui-collapse model-collapse queryCondition" lay-filter="">
        <div class="layui-colla-item model-item">
            <h2 class="layui-colla-title"><i class="iconfont">&#xe66f;</i>审计安排</h2>
            <div class="layui-colla-content layui-show" id="auditInfoThreeContent"></div>
        </div>
    </div>
    <!--审计费及考核评价-->
    <!--<div class="layui-collapse model-collapse queryCondition" lay-filter="">-->
        <!--<div class="layui-colla-item model-item">-->
            <!--<h2 class="layui-colla-title"><i class="iconfont">&#xe66f;</i>审计费及考核评价</h2>-->
            <!--<div class="layui-colla-content layui-show" id="auditInfoThreeContent2"></div>-->
        <!--</div>-->
    <!--</div>-->

    <!--重点核查信息开始-->
    <div class="layui-collapse model-collapse queryCondition"  id="keyVerifyDiv" style="display: none">
        <div class="layui-colla-item model-item">
            <h2 class="layui-colla-title"><i class="iconfont">&#xe66f;</i>重点核查信息</h2>
            <div class="layui-colla-content layui-show" id="keyVerifyInfoDiv">  </div>
        </div>
    </div>
    <!--重点核查信息结束-->

    <!--复核审计信息开始-->
    <div class="layui-collapse model-collapse queryCondition" lay-filter="" id="reviewAudit" style="display: none">
        <div class="layui-colla-item model-item">
            <h2 class="layui-colla-title"><i class="iconfont">&#xe65c;</i>复核审计信息</h2>
            <div class="layui-colla-content layui-show" id="reviewAuditDiv">  </div>
        </div>
    </div>

</div>
<!--#include virtual ="include/version.html"-->
<script src="resource/js/audit/prj/endaccount/taskinfo/endTaskInfo.js?v=6.5" type="text/javascript"></script>
<script type="text/javascript">
    var $, businessKey, auditType, provinceId, areaId, processDefinition, taskDefinitionKey, processInstanceId;
    layui.use(['jquery', 'jqelem', 'jqajax', 'jqfrm'], function () {
        var element = layui.jqelem,
            ctx = top.global.ctx,
            frm = layui.jqfrm;
        var jqajax = layui.jqajax;
        $ = layui.jquery;
        element.init()
        businessKey = getUrlParam("businessKey");
        processDefinition = getUrlParam("processDefinitionId");
        taskDefinitionKey = getUrlParam("taskDefinitionKey");
        processInstanceId = getUrlParam("processInstanceId");
        //查询重点核查信息数据
        window.queryMainPointCheckInfo = function(){

            var isEndCheckSelectShow = '1';//需要通过该字段断"是否重点核查 是否展示。1展示，0不展示(黑龙江，地市审计部不能选经查)
            //是否展示再审计
            $.ajax({
                url: ctx + "/prj/end/fix/isEndCheckShowInfo",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({
                    endAccountId: businessKey
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (rsp) {
                    if (rsp.httpCode == 200) {
                        isEndCheckSelectShow  = rsp.data.isEndCheckSelectShow;
                        $.ajax({
                            url: ctx + "/prj/reAuditEnterReviewAuditResult/reviewAuditKeyCheckInfo/"+businessKey,
                            dataType: "json",
                            type: "POST",
                            contentType: "application/json;charset=UTF-8",
                            data: JSON.stringify({endAccountId: businessKey}),
                            success: function (res) {
                                if (200 == res.httpCode) {
                                    var data = res.data;
                                    if(data.isReAuditCheck=='1'||isEndCheckSelectShow=='0'){
                                        var isKeyCheck =data.isKeyCheck;//是否重点核查
                                        initIframeItem1(isKeyCheck);
                                    }

                                }

                            }, error: function (e) {
                                frm.error("接口请求失败！");
                            }
                        });
                    } else {
                        frm.error("请求失败！");
                    }
                },
                error: function (rsp) {
                    frm.error("网络连接失败！");
                }
            });

        };
        /**
         * 获取流程推进信息
         */
        $.ajax({
            url: ctx + "/prj/end/fix/receiveAudit/selectReceive",
            type: "POST",
            dataType: "json",
            contentType: "application/json;charset=UTF-8",
            data: JSON.stringify({endAccountId: businessKey}),
            success: function (res) {
                auditType = res.data.auditTypeId;
                provinceId = res.data.provinceId;
                areaId = res.data.areaId;
                if ("1" == auditType) {
                    $("#auditCostDiv").hide();
                }
                initIframeItem(processDefinition, res.data.auditTypeOther);
                queryMainPointCheckInfo();
            }
        });

        window.error2 = function (obj) {
            frm.error(obj);
        }
    });

    /**
     * 初始化iframe条目
     */
    function initIframeItem(processDefinition, businessType) {
        var projectIframe, manageIframe, auditIframe, auditInfoFourIframe, auditInfoFourIframe2;
        //项目信息iframe
        if (businessType && "3" === businessType) {
            projectIframe = "<iframe style='height: 80px; overflow: auto!important;'" +
                "class='jqadmin-iframe' src='views/audit/prj/endaccount/taskhasdo/beenDoneShow/otherProjectInfo.html?endAccountId=" + businessKey +
                "&taskDefinitionKey=" + taskDefinitionKey + "&processInstanceId=" + processInstanceId + "'></iframe>";
        } else {
            projectIframe = "<iframe style='height: 185px; overflow: auto!important;'" +
                "class='jqadmin-iframe' src='views/audit/prj/endaccount/taskhasdo/beenDoneShow/projectInfo.html?endAccountId=" + businessKey +
                "&taskDefinitionKey=" + taskDefinitionKey + "&processInstanceId=" + processInstanceId + "'></iframe>";
        }

        //管理信息iframe
        manageIframe = '<iframe id = "manageIframe" style="height: 400px;overflow: auto!important;" class="jqadmin-iframe" data-id="0" ' +
            'src="views/audit/prj/endaccount/tabtodo/flow/commonShow/manageHasEndInfo.html?endAccountId=' + businessKey + '&businessType=' + businessType + '"></iframe>';


        //审计信息iframe
        // auditIframe = "<iframe style='height: 450px; overflow: auto!important;'" +
        //     "class='jqadmin-iframe' src='views/audit/prj/endaccount/tabtodo/flow/displayAuditResult/provdisReportInfoResultShow.html?endAccountId=" + businessKey + "'></iframe>";
        auditIframe = "<iframe style='height: 450px; overflow: auto!important;'" +
            "class='jqadmin-iframe' src='views/audit/prj/endaccount/tabtodo/flow/cost/provCostDetailShow2.html?endAccountId=" + businessKey + "'></iframe>";

        //审计安排
        auditInfoFourIframe = "<iframe id='auditInfoFourIframe' style='height: 400px; overflow: auto!important;'" +
            "class='jqadmin-iframe' src='views/audit/prj/endaccount/tabtodo/flow/auditPlan/accountAndAgencyAllInfo.html?endAccountId=" + businessKey + "'></iframe>";

        //审计费及考核评价
        // auditInfoFourIframe2 = "<iframe style='height: 300px; overflow: auto!important;'" +
        //     "class='jqadmin-iframe' src='views/audit/prj/endaccount/taskhasdo/beenDoneShow/provCostDetailShow.html?endAccountId=" + businessKey + "'></iframe>";



        // $("#auditInfoThreeContent2").html(auditInfoFourIframe2);
        $("#projectInfoContent").html(projectIframe);
        $("#managementInfoContent").html(manageIframe);
        $("#auditInfoContent").html(auditIframe);
        $("#auditInfoThreeContent").html(auditInfoFourIframe);


    }
    function initIframeItem1(isKeyCheck){
        var keyVerifyInfoDiv,reviewAuditDiv;
        if(isKeyCheck==''){

        }else if(isKeyCheck=='0'){
            $('#keyVerifyDiv').show();
            keyVerifyInfoDiv  = '<iframe id = "keyVerifyInfoIframe" style="height: 200px; overflow-y: auto!important;" class="jqadmin-iframe" data-id="0"  src="views/audit/prj/endaccount/tabtodo/flow/auditPlan/keyVerificationInformationResultEntryDone.html?endAccountId=' + businessKey + '&linkKey=' + taskDefinitionKey + '"></iframe>';
            $("#keyVerifyInfoDiv").html(keyVerifyInfoDiv);
        }else{
            $('#keyVerifyDiv').show();
            keyVerifyInfoDiv  = '<iframe id = "keyVerifyInfoIframe" style="height: 200px; overflow-y: auto!important;" class="jqadmin-iframe" data-id="0"  src="views/audit/prj/endaccount/tabtodo/flow/auditPlan/keyVerificationInformationResultEntryDone.html?endAccountId=' + businessKey + '&linkKey=' + taskDefinitionKey + '"></iframe>';
            $("#keyVerifyInfoDiv").html(keyVerifyInfoDiv);

            $('#reviewAudit').show();
            // reviewAuditDiv = '<iframe id = "reviewAuditDivIframe" style="height: 200px; overflow-y: auto!important;" class="jqadmin-iframe" data-id="0"  src="views/audit/prj/endaccount/tabtodo/flow/cost/reviewAuditInfoViewDone.html?cutModeEnableEdit=1&endAccountId=' + businessKey + '&linkKey=' + taskDefinitionKey + '"></iframe>';
            reviewAuditDiv = '<iframe id = "reviewAuditDivIframe" style="height: 200px; overflow-y: auto!important;" class="jqadmin-iframe" data-id="0"  src="views/audit/prj/endaccount/tabtodo/flow/cost/reviewAuditInfoView2.html?cutModeEnableEdit=1&endAccountId=' + businessKey + '&linkKey=' + taskDefinitionKey + '"></iframe>';
            $("#reviewAuditDiv").html(reviewAuditDiv);
        }

    };

    function error(obj) {
        error2(obj);
    }

    function loadProcessData() {
        var processData = {provinceId: provinceId, areaId: areaId, auditType: auditType};
        return processData;
    }

    /**
     * 流程图个性化
     * @returns {string}
     */
    function identityIdData() {
        return provinceId;
    }

    function passValidate() {
        //页面未加载完毕的时候提示
        var frm = layui.jqfrm;
        if ('undefined' == provinceId || provinceId == null || provinceId == '') {
            frm.error("页面尚未加载完毕，请稍后重试！");
            return false;
        }
        return true;
    }

    function backValidate() {
        //页面未加载完毕的时候提示
        var frm = layui.jqfrm;
        if ('undefined' == provinceId || provinceId == null || provinceId == '') {
            frm.error("页面尚未加载完毕，请稍后重试！");
            return false;
        }
        return true;
    }
</script>
</body>
</html>
