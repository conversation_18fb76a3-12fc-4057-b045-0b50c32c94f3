<!--#include virtual ="include/header.html"-->
<!--
	审计部员工确认审计结果
-->
<body>
<style>
    .layui-form-label{
        width: 102px;
    }
    .layui-input-block{
        margin-left: 102px;
    }
    .layui-disabled, .layui-disabled:hover {
        color:#958a8a !important;
    }
</style>
<section class="panel panel-padding">
    <form class="layui-form" id="busiFrm">
        <input id="auditType" name="auditType" type="hidden" value="">
        <input id="provinceId" name="provinceId" type="hidden" value="">
        <input id="areaId" name="areaId" type="hidden" value="">
        <input id="isExtract" name="isExtract" type="hidden"/>
        <input id="provVar" name="provVar" type="hidden"/>
        <input id="areaVar" name="areaVar" type="hidden"/>
        <div class="layui-row layui-col-space10 fixed-height">
            <div class="layui-col-xs12 fixed-head">
                <div class="table-title-infor" id="otherProject" style="display: none">费用类项目审计：<input class="linkName"
                                                                                                      disabled
                                                                                                      style="width: 350px;border: none;background-color: #FFFFFF"
                                                                                                      type="text"/></div>
                <div class="table-title-infor" id="endProject" style="display: none">非抽审项目结算审计：<input class="linkName"
                                                                                                      disabled
                                                                                                      style="width: 350px;border: none;background-color: #FFFFFF"
                                                                                                      type="text"/></div>
                <div class="table-title-infor" id="extractProject" style="display: none">抽审项目结算审计：<input
                        class="linkName" disabled style="width: 350px;border: none;background-color: #FFFFFF"
                        type="text"/></div>
            </div>
        </div>
        <!--项目信息开始-->
        <div class="layui-collapse model-collapse queryCondition" lay-filter="">
            <div class="layui-colla-item model-item">
                <h2 class="layui-colla-title"><i class="iconfont">&#xe6e3;</i>项目信息</h2>
                <div class="layui-colla-content layui-show" id="projectInfoIframeDiv">
                    <!--<iframe id = "projectIframe" class="jqadmin-iframe" data-id='0' src="commonShow/projectInfo.html"></iframe>-->
                </div>
            </div>
        </div>
        <!--项目信息结束-->
        <!--管理信息开始-->
        <div class="layui-collapse model-collapse queryCondition" lay-filter="">
            <div class="layui-colla-item model-item">
                <h2 class="layui-colla-title"><i class="iconfont">&#xe6a8;</i>管理信息</h2>
                <div class="layui-colla-content layui-show" id="manageIframeDiv"></div>
            </div>
        </div>
        <!--管理信息结束-->
        <!--审计安排开始-->
        <div class="layui-collapse model-collapse queryCondition" lay-filter="">
            <div class="layui-colla-item model-item">
                <h2 class="layui-colla-title"><i class="iconfont">&#xe66f;</i>审计安排</h2>
                <div class="layui-colla-content layui-show" id="accountAndAgencyAllInfoIframeDiv"></div>
            </div>
        </div>
        <!--审计安排结束-->
        <!--审计费信息开始-->
        <div class="layui-collapse model-collapse queryCondition"  lay-filter="">
            <div class="layui-colla-item model-item">
                <h2 class="layui-colla-title"><i class="iconfont">&#xe65c;</i>审计信息</h2>
                <div class="layui-colla-content layui-show" id="costIframeDiv"></div>
            </div>
        </div>
        <!--审计费信息结束-->
        <!--审计信息开始-->
        <!--<div class="layui-collapse model-collapse queryCondition" lay-filter="">-->
            <!--<div class="layui-colla-item model-item">-->
                <!--<h2 class="layui-colla-title"><i class="iconfont">&#xe65c;</i>审计信息</h2>-->
                <!--<div class="layui-colla-content layui-show" id="reportIframeDiv"></div>-->
            <!--</div>-->
        <!--</div>-->
        <!--审计信息结束-->
        <!--附件上传开始-->
        <div class="layui-collapse model-collapse queryCondition" lay-filter="">
            <div class="layui-colla-item model-item">
                <h2 class="layui-colla-title"><i class="iconfont">&#xe674;</i>附件上传</h2>
                <div class="layui-colla-content layui-show new-colla-content" id="bookContrastIframeDiv"></div>
            </div>
        </div>
        <!--附件上传结束-->

        <!--重点核查信息开始-->
        <div class="layui-collapse model-collapse queryCondition" lay-filter="" id="keyVerifyDiv" style="display: none">
            <div class="layui-colla-item model-item">
                <h2 class="layui-colla-title"><i class="iconfont">&#xe66f;</i>重点核查信息</h2>
                <div class="layui-colla-content layui-show" id="keyVerifyInfoDiv">
                    <form class="layui-form" action="" lay-filter="isReAuditCheckFrom">
                        <div class="layui-form-item">
                            <label class="layui-form-label">
                                <b style="color: red;">*</b>
                                是否再审计复核</label>
                            <div class="layui-input-block">
                                <input type="radio" lay-filter="isReAuditCheck" name="isReAuditCheck" value="1" title="是">
                                <input type="radio" lay-filter="isReAuditCheck" name="isReAuditCheck" value="0" title="否">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!--重点核查信息结束-->
    </form>
</section>
</body>
<!--#include virtual ="include/version.html"-->
<script src="resource/js/audit/prj/endaccount/taskinfo/endTaskInfo.js?v=6.5" type="text/javascript"></script>
<script type="text/javascript">
    var auditTypeId;
    var isEndCheckShow=1;//是否展示再审计选择
    var resultFinal = false;
    //选择是否再审计复核标志
    var chooseReFlag = false;
    //是否再审计复核
    var isReAuditCheck;
    layui.use(['jqfrm', 'jqbind', 'laydate', 'jqelem'], function () {
        var jqbind = layui.jqbind,
            form = layui.jqform,
            $ = layui.jquery,
            jqajax = layui.jqajax,
            frm = layui.jqfrm,
            ctx = top.global.ctx,
            element = layui.jqelem,
            table = layui.table,
            laydate = layui.laydate;
        element.init();

        var endAccountId = getUrlParam("businessKey");
        $("#endAccountId").val(endAccountId);
        linkKey = getUrlParam("taskDefinitionKey");
        var a = getUrlParam("taskDefinitionKey");
        var b = getUrlParam("taskId");
        var processDefinition = getUrlParam("processDefinitionId");


        //获取自审or委托
        $.ajax({
            url: ctx + "/prj/end/fix/receiveAudit/selectReceive",
            type: "POST",
            dataType: "json",
            data: JSON.stringify({
                endAccountId: endAccountId
            }),
            async: false,
            contentType: "application/json;charset=UTF-8",
            success: function (rsp) {
                if (rsp.httpCode == 200) {
                    if ('1' == rsp.data.auditTypeOther) {
                        if ('1' != rsp.data.isExtract) {
                            $("#endProject").show();
                        } else {
                            $("#extractProject").show();
                        }
                    } else {
                        $("#otherProject").show();
                    }
                    auditTypeId = rsp.data.auditTypeId;
                    var provinceId = rsp.data.provinceId;
                    var areaId = rsp.data.areaId;
                    $("#auditType").val(auditTypeId);
                    $("#provinceId").val(provinceId);
                    $("#areaId").val(areaId);
                    $("#isExtract").val(rsp.data.isExtract);
                    if (auditTypeId == '1') {
                    }
                    form.render();
                } else {
                    frm.error("请求失败！");
                }
            },
            error: function (rsp) {
                frm.error("网络连接失败！");
            }
        });


        //是否展示再审计
        $.ajax({
            url: ctx + "/prj/end/fix/isEndCheckShowInfo",
            type: "POST",
            dataType: "json",
            data: JSON.stringify({
                endAccountId: endAccountId
            }),
            contentType: "application/json;charset=UTF-8",
            success: function (rsp) {
                if (rsp.httpCode == 200) {
                    isEndCheckShow  = rsp.data.isEndCheckShow;
                    if(isEndCheckShow=='0'||linkKey.indexOf("EndAccountOther") > -1){
                        $('#keyVerifyDiv').hide();
                        chooseReFlag = true;
                    }else{
                        $('#keyVerifyDiv').show();
                    }
                    var editFlag = rsp.data.editFlag;
                    if(!editFlag){
                       //todo 是否再审计不可编辑
                        $('[name="isReAuditCheck"]').attr('disabled',true)
                    }
                    form.render();
                } else {
                    frm.error("请求失败！");
                }
            },
            error: function (rsp) {
                frm.error("网络连接失败！");
            }
        });

        form.on('radio(isReAuditCheck)', function (data) {
            isReAuditCheck = data.value;   //  当前选中的value值
            setIsReAuditCheck()
        });
        function setIsReAuditCheck(){
            $.ajax({
                url: ctx + "/prj/end/send/setIsReAuditCheck",
                type: "POST",
                dataType: "json",
                data: JSON.stringify({
                    endAccountId: endAccountId,
                    isReAuditCheck: isReAuditCheck
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (rsp) {
                    if(rsp.httpCode != 200){
                        frm.error(rsp.msg);
                        //todo 恢复原先选中的值
                    }else{
                        chooseReFlag = true;
                    }
                }
            });
        }
        //获取是否再审计复核值和省分、地市对应流程值
        function getIsReAuditCheck(){
            $.ajax({
                url: ctx + "/prj/end/send/getIsReAuditCheck",
                type: "POST",
                dataType: "json",
                async:false,
                data: JSON.stringify({
                    endAccountId: endAccountId
                }),
                contentType: "application/json;charset=UTF-8",
                success: function (rsp) {
                    if (rsp.httpCode == 200) {
                        $("#provVar").val(rsp.data.provVar);
                        $("#areaVar").val(rsp.data.areaVar);
                        isReAuditCheck = rsp.data.isReAuditCheck;
                        //表单赋值
                        if (isReAuditCheck === "0") {
                            $('input[name="isReAuditCheck"][value="0"]').prop("checked", true);
                            chooseReFlag = true;
                        } else if (isReAuditCheck === "1") {
                            $('input[name="isReAuditCheck"][value="1"]').prop("checked", true);
                            chooseReFlag = true;
                        } else {
                            $('input[name="isReAuditCheck"][value="0"]').prop("checked", false);
                            $('input[name="isReAuditCheck"][value="1"]').prop("checked", false);
                            chooseReFlag = false;
                        }
                    } else {
                        frm.error("请求失败！");
                    }
                },
                error: function (rsp) {
                    frm.error("网络连接失败！");
                }
            });
        }

        $(function () {
            // 设置iframe高度
            var iframeHeight1 = $("#bookIframe").contents().find("body").height();
            $("#bookIframe").height(iframeHeight1);
            var iframeHeight11 = $("#costIframe").contents().find("body").height();
            $("#costIframe").height(iframeHeight11);
            var iframeHeight3 = $("#reportIframe").contents().find("body").height();
            $("#reportIframe").height(iframeHeight3);
            var iframeHeight10 = $("#reportIframe2").contents().find("body").height();
            $("#reportIframe2").height(iframeHeight10);
            var iframeHeight4 = $("#worksheetIframe").contents().find("body").height();
            $("#worksheetIframe").height(iframeHeight4);
            var iframeHeight5 = $("#reviewInventoryIframe").contents().find("body").height();
            $("#reviewInventoryIframe").height(iframeHeight5);
            var iframeHeight7 = $("#projectIframe").contents().find("body").height();
            $("#projectIframe").height(iframeHeight7);
            var iframeHeight8 = $("#projectInfoIframe").contents().find("body").height();
            $("#projectInfoIframe").height(iframeHeight8);
            var iframeHeight9 = $("#managementIframe").contents().find("body").height();
            $("#managementIframe").height(iframeHeight9);

            var iframeHeight13 = $("#keyVerifyInfoDiv").contents().find("body").height();
            $("#keyVerifyInfoDiv").height(iframeHeight13);
            setParams(processDefinition, auditTypeId);
        });

        form.init({
            form: "#busiFrm"
        });

        function setParams(processDefinition, auditTypeId) {
            var html = '';

            if (processDefinition.indexOf("EndAccountOther") > -1) {
                html = '<iframe id = "projectInfoIframe" style="height: 90px;overflow: auto!important;" class="jqadmin-iframe" data-id="0" src="views/audit/prj/endaccount/othersend/othersendcontent/otherProjectInfoShow.html?endAccountId=' + endAccountId + '"></iframe>';
                document.getElementById('projectInfoIframeDiv').innerHTML = html;
            } else {
                html = '<iframe id = "projectInfoIframe" style="height: 150px;overflow: auto!important;" class="jqadmin-iframe" data-id="0" src="views/audit/prj/endaccount/tabtodo/flow/commonShow/projectInfo.html?endAccountId=' + endAccountId + '"></iframe>';
                document.getElementById('projectInfoIframeDiv').innerHTML = html;
            }

            if (processDefinition.indexOf("EndAccountOther") > -1) {
                html = '<iframe id="manageInfoIframe" class="jqadmin-iframe" style="height: 300px; overflow: hidden;" src="views/audit/prj/endaccount/othersend/othersendcontent/managementInfoShow.html?endAccountId=' + endAccountId + '"></iframe>';
                document.getElementById("manageIframeDiv").innerHTML = html;
            } else {
                html = '<iframe id = "manageInfoIframe" style="height: 375px;overflow: auto!important;" class="jqadmin-iframe" data-id="0" src="views/audit/prj/endaccount/tabtodo/flow/commonShow/manageHasEndInfo.html?endAccountId=' + endAccountId + '"></iframe>';
                document.getElementById('manageIframeDiv').innerHTML = html;
            }

            // html = '<iframe id = "reportIframe" style="height: 260px;overflow: auto!important;" class="jqadmin-iframe" data-id="0"  src="views/audit/prj/endaccount/tabtodo/flow/displayAuditResult/provdisReportInfoResultEdit.html?endAccountId=' + endAccountId + '&linkKey=' + linkKey + '"></iframe>';
            // document.getElementById('reportIframeDiv').innerHTML = html;

            html = '<iframe id = "costIframe" style="height: 200px;overflow: auto!important;" class="jqadmin-iframe" data-id="0"  src="views/audit/prj/endaccount/tabtodo/flow/cost/provCostDetail1.html?endAccountId=' + endAccountId + '&linkKey=' + linkKey + '&auditTypeId='+auditTypeId+'"></iframe>';
            document.getElementById('costIframeDiv').innerHTML = html;

            html = '<iframe id = "bookContrastIframe" style="height: 200px; overflow-y: auto!important;" class="jqadmin-iframe" data-id="0"  src="views/audit/prj/endaccount/tabtodo/flow/auditPlan/otherFileUpload.html?endAccountId=' + endAccountId + '&linkKey=' + linkKey + '"></iframe>';
            document.getElementById('bookContrastIframeDiv').innerHTML = html;

            html = '<iframe id = "accountAndAgencyAllInfoIframe" style="height: 300px;overflow-y: auto!important;" class="jqadmin-iframe" data-id="0" src="views/audit/prj/endaccount/tabtodo/flow/auditPlan/accountAndAgencyAllInfo.html?endAccountId=' + endAccountId + '"></iframe>';
            document.getElementById('accountAndAgencyAllInfoIframeDiv').innerHTML = html;
            getIsReAuditCheck()
            var provVar = document.getElementById("provVar").value;
            var areaVar = document.getElementById("areaVar").value;
            // if (processDefinition.indexOf(provVar) > -1 || processDefinition.indexOf(areaVar) > -1) {
            //     $("#keyVerifyDiv").show();
            // }else {
            //     $("#keyVerifyDiv").hide();
            //     chooseReFlag = true;
            // }


        }

        // 验证评分
        window.validateAgencyScore = function () {
            var reTemp = false;
            if(auditTypeId=='1'){
                reTemp = true;
            }else{
                $.ajax({
                    url: ctx + "/prj/endAccount/agencyScore/getDiffScore/" + endAccountId,
                    dataType: "json",
                    type: "post",
                    async: false,
                    contentType: "application/json;charset=UTF-8",
                    success: function (ret) {
                        if (ret.httpCode == 200) {
                            if (ret.data == null || ret.data == '') {
                                reTemp = true;
                            } else if (ret.data.length > 0) {
                                if (ret.data.length == 1) {
                                    var score = ret.data[0];
                                    if (score.flagMark == '1') {
                                        frm.validate("【审计信息】未进行【工程结算委托审计项目服务】考核评分！");
                                        return false;
                                    }
                                }
                                var info = '';
                                for (var i = 0; i < ret.data.length; i++) {
                                    info += ret.data[i].orderId;
                                    if (i != ret.data.length - 1) {
                                        info += "； "
                                    }
                                }
                                frm.validate("以下合同/订单的考核评分发生变化，请在【审计信息】中对【考核打分】重新保存！<br>" + info);
                            } else {
                                reTemp = true;
                            }
                        }
                    }
                });
            }

            return reTemp;
        }

    });

    function passButCost() {
        var btn = parent.document.getElementById('passButton');
        btn.click();
    }

    /**
     * 流程图个性化
     * @returns {string}
     */
    function identityIdData() {
        var provinceId = document.getElementById('provinceId').value;
        return provinceId;
    }

    // 流程提交环节和人所需变量
    function loadProcessData() {
        var auditTypeId = document.getElementById('auditType').value;
        var provinceId = document.getElementById('provinceId').value;
        var areaId = document.getElementById('areaId').value;
        //isCheck 是否审计领导复核：1：是，0：否
        var isCheck = document.getElementById('manageInfoIframe').contentWindow.document.getElementById('isCheck').value;
        var isExtract = document.getElementById("isExtract").value;
        if(isEndCheckShow=='0'){
            isReAuditCheck = '0';
        }
        var processData = {
            auditType: auditTypeId,
            provinceId: provinceId,
            areaId: areaId,
            isCheck: isCheck,
            isReAuditCheck:isReAuditCheck
        };

        if ("1" == isExtract) {
            processData.isExtract = 1;
        } else {
            processData.isExtract = 0;
        }
        return processData;
    }

    function passValidate() {
        //页面未加载完毕的时候提示
        var frm = layui.jqfrm;
        var provinceId = document.getElementById('provinceId').value;
        if ('undefined' == provinceId || provinceId == null || provinceId == '') {
            frm.error("页面尚未加载完毕，请稍后重试！");
            return false;
        }
       if(isEndCheckShow=='1'&&linkKey.indexOf("EndAccountOther") < 0){
            if (!chooseReFlag) {
                layer.alert('请在重点核查信息中选择是否再审计复核！', {icon: 2, title: '提示'});
                return false;
            }
       }


        if (resultFinal) {
            return resultFinal;
        }

        if (!validateAgencyScore()) {
            resultFinal = false;
            return false;
        }

        if (!document.getElementById('costIframe').contentWindow.saveCostData()) {
            resultFinal = false;
            return false;
        }
        if (!document.getElementById('costIframe').contentWindow.checkCostOrderFun()) {
            resultFinal = false;
            return false;
        }
        // 更新审计费订单状态
        document.getElementById('costIframe').contentWindow.updateFeeStatus();

        var layerf = window.top.layer.confirm("【审计信息】【查看审计结果】页下【施工单位承担审计费金额】，是否确认无误？", {
            btn: ['确定', '取消'] //按钮
        }, function (index) {
            window.top.layer.close(index);
            resultFinal = true;
            var btn = parent.document.getElementById('passButton');
            btn.click();
        });
        return false;
    }

    /**
     * 提交时，有后台业务操作，点击关闭时，刷新flag
     */
    function refreshFlagData() {
        resultFinal = false;
    }


    function backValidate() {
        //页面未加载完毕的时候提示
        var frm = layui.jqfrm;
        var provinceId = document.getElementById('provinceId').value;
        if ('undefined' == provinceId || provinceId == null || provinceId == '') {
            frm.error("页面尚未加载完毕，请稍后重试！");
            return false;
        }

        return true;
    }
</script>
</html>
